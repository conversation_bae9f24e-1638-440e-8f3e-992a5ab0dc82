<?php
/**
 * Script de migration pour créer les tables d'évaluation
 * et migrer les données existantes si nécessaire
 */

require_once __DIR__ . '/config/database.php';

echo "<h1>Migration des Tables d'Évaluation</h1>";

try {
    // Étape 1: Créer les nouvelles tables
    echo "<h2>Étape 1: Création des tables</h2>";
    
    // Table des prestataires pour les évaluations
    $db->exec("CREATE TABLE IF NOT EXISTS evaluation_prestataires (
        id INT AUTO_INCREMENT PRIMARY KEY,
        nom VARCHAR(255) NOT NULL UNIQUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )");
    echo "✅ Table 'evaluation_prestataires' créée<br>";

    // Table des services pour les évaluations
    $db->exec("CREATE TABLE IF NOT EXISTS evaluation_services (
        id INT AUTO_INCREMENT PRIMARY KEY,
        nom VARCHAR(255) NOT NULL UNIQUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )");
    echo "✅ Table 'evaluation_services' créée<br>";

    // Table des évaluations
    $db->exec("CREATE TABLE IF NOT EXISTS evaluations (
        id INT AUTO_INCREMENT PRIMARY KEY,
        prestataire VARCHAR(255) NOT NULL,
        service VARCHAR(255) NOT NULL,
        qualite INT NOT NULL CHECK (qualite >= 1 AND qualite <= 5),
        delai INT NOT NULL CHECK (delai >= 1 AND delai <= 5),
        communication INT NOT NULL CHECK (communication >= 1 AND communication <= 5),
        commentaires TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_prestataire (prestataire),
        INDEX idx_service (service),
        INDEX idx_created_at (created_at)
    )");
    echo "✅ Table 'evaluations' créée<br>";

    // Étape 2: Insérer les données par défaut
    echo "<h2>Étape 2: Insertion des données par défaut</h2>";
    
    $defaultProviders = [
        'Prestataire A',
        'Prestataire B', 
        'Prestataire C',
        'Entreprise Alpha',
        'Entreprise Beta'
    ];
    
    $stmt = $db->prepare("INSERT IGNORE INTO evaluation_prestataires (nom) VALUES (?)");
    $providerCount = 0;
    foreach ($defaultProviders as $provider) {
        if ($stmt->execute([$provider])) {
            $providerCount++;
        }
    }
    echo "✅ $providerCount prestataires par défaut ajoutés<br>";

    $defaultServices = [
        'Installation',
        'Maintenance',
        'Réparation',
        'Consultation',
        'Formation',
        'Support technique',
        'Audit',
        'Nettoyage'
    ];
    
    $stmt = $db->prepare("INSERT IGNORE INTO evaluation_services (nom) VALUES (?)");
    $serviceCount = 0;
    foreach ($defaultServices as $service) {
        if ($stmt->execute([$service])) {
            $serviceCount++;
        }
    }
    echo "✅ $serviceCount services par défaut ajoutés<br>";

    // Étape 3: Migrer les données existantes si nécessaire
    echo "<h2>Étape 3: Migration des données existantes</h2>";
    
    // Vérifier s'il y a des évaluations existantes
    $stmt = $db->query("SELECT COUNT(*) as count FROM evaluations");
    $existingEvaluations = $stmt->fetch()['count'];
    
    if ($existingEvaluations > 0) {
        echo "📊 $existingEvaluations évaluations existantes trouvées<br>";
        
        // Migrer les prestataires uniques depuis les évaluations
        $stmt = $db->query("
            INSERT IGNORE INTO evaluation_prestataires (nom) 
            SELECT DISTINCT prestataire 
            FROM evaluations 
            WHERE prestataire NOT IN (SELECT nom FROM evaluation_prestataires)
        ");
        $migratedProviders = $stmt->rowCount();
        echo "✅ $migratedProviders prestataires migrés depuis les évaluations<br>";
        
        // Migrer les services uniques depuis les évaluations
        $stmt = $db->query("
            INSERT IGNORE INTO evaluation_services (nom) 
            SELECT DISTINCT service 
            FROM evaluations 
            WHERE service NOT IN (SELECT nom FROM evaluation_services)
        ");
        $migratedServices = $stmt->rowCount();
        echo "✅ $migratedServices services migrés depuis les évaluations<br>";
    } else {
        echo "ℹ️ Aucune évaluation existante trouvée<br>";
    }

    // Étape 4: Vérification finale
    echo "<h2>Étape 4: Vérification finale</h2>";
    
    $stmt = $db->query("SELECT COUNT(*) as count FROM evaluation_prestataires");
    $totalProviders = $stmt->fetch()['count'];
    echo "📊 Total prestataires: $totalProviders<br>";
    
    $stmt = $db->query("SELECT COUNT(*) as count FROM evaluation_services");
    $totalServices = $stmt->fetch()['count'];
    echo "📊 Total services: $totalServices<br>";
    
    $stmt = $db->query("SELECT COUNT(*) as count FROM evaluations");
    $totalEvaluations = $stmt->fetch()['count'];
    echo "📊 Total évaluations: $totalEvaluations<br>";

    // Étape 5: Test des fonctionnalités
    echo "<h2>Étape 5: Test des fonctionnalités</h2>";
    
    // Tester l'ajout d'un prestataire
    $testProvider = "Test Prestataire " . date('Y-m-d H:i:s');
    $stmt = $db->prepare("INSERT INTO evaluation_prestataires (nom) VALUES (?)");
    if ($stmt->execute([$testProvider])) {
        echo "✅ Test d'ajout de prestataire réussi<br>";
        
        // Supprimer le prestataire de test
        $stmt = $db->prepare("DELETE FROM evaluation_prestataires WHERE nom = ?");
        $stmt->execute([$testProvider]);
        echo "✅ Test de suppression de prestataire réussi<br>";
    }
    
    // Tester l'ajout d'un service
    $testService = "Test Service " . date('Y-m-d H:i:s');
    $stmt = $db->prepare("INSERT INTO evaluation_services (nom) VALUES (?)");
    if ($stmt->execute([$testService])) {
        echo "✅ Test d'ajout de service réussi<br>";
        
        // Supprimer le service de test
        $stmt = $db->prepare("DELETE FROM evaluation_services WHERE nom = ?");
        $stmt->execute([$testService]);
        echo "✅ Test de suppression de service réussi<br>";
    }

    echo "<h2>🎉 Migration terminée avec succès!</h2>";
    echo "<p><strong>Le système d'évaluation est maintenant prêt à être utilisé.</strong></p>";
    
    echo "<h3>Prochaines étapes:</h3>";
    echo "<ul>";
    echo "<li><a href='evaluation.php'>Tester le formulaire d'évaluation</a></li>";
    echo "<li><a href='test_evaluation_system.php'>Exécuter les tests complets</a></li>";
    echo "<li><a href='historique_evaluations.php'>Voir l'historique des évaluations</a></li>";
    echo "<li><a href='moyenne_satisfaction.php'>Voir les moyennes de satisfaction</a></li>";
    echo "</ul>";

} catch (Exception $e) {
    echo "<h2>❌ Erreur lors de la migration</h2>";
    echo "<p>Erreur: " . $e->getMessage() . "</p>";
    echo "<p>Trace: " . $e->getTraceAsString() . "</p>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 800px;
    margin: 20px auto;
    padding: 20px;
    background-color: #f4f4f4;
}

h1, h2 {
    color: #f57c00;
}

a {
    color: #f57c00;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}

ul {
    background-color: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
</style>
