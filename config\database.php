<?php
/**
 * Gestionnaire de base de données centralisé pour Schluter Systems
 * Utilise le pattern Singleton pour une gestion optimisée des connexions
 */

class SchlutterDatabase {
    private static $instance = null;
    private $connection = null;
    private $config = null;

    /**
     * Obtenir la configuration de la base de données depuis l'environnement
     */
    private function getDbConfig() {
        if ($this->config === null) {
            // Charger la configuration d'environnement
            if (!defined('SCHLUTER_CONFIG')) {
                require_once __DIR__ . '/environment.php';
            }
            $this->config = SCHLUTER_CONFIG['database'];
        }
        return $this->config;
    }

    /**
     * Constructeur privé pour empêcher l'instanciation directe
     */
    private function __construct() {
        $this->connect();
    }

    /**
     * Empêche le clonage de l'instance
     */
    private function __clone() {}

    /**
     * Empêche la désérialisation de l'instance
     */
    public function __wakeup() {
        throw new Exception("Cannot unserialize singleton");
    }

    /**
     * Retourne l'instance unique de la classe
     */
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Établit la connexion à la base de données
     */
    private function connect() {
        try {
            $config = $this->getDbConfig();

            // Créer la base de données si elle n'existe pas
            $this->createDatabaseIfNotExists();

            // Se connecter à la base de données
            $dsn = sprintf(
                "mysql:host=%s;dbname=%s;charset=%s",
                $config['host'],
                $config['dbname'],
                $config['charset']
            );

            $this->connection = new PDO(
                $dsn,
                $config['username'],
                $config['password'],
                $config['options']
            );

            // Log de connexion en développement
            if (defined('SCHLUTER_DEBUG') && SCHLUTER_DEBUG) {
                logInfo("Connexion à la base de données établie", [
                    'host' => $config['host'],
                    'database' => $config['dbname']
                ]);
            }

        } catch (PDOException $e) {
            logError("Erreur de connexion à la base de données: " . $e->getMessage());
            throw new Exception("Impossible de se connecter à la base de données");
        }
    }

    /**
     * Crée la base de données si elle n'existe pas
     */
    private function createDatabaseIfNotExists() {
        try {
            $config = $this->getDbConfig();

            $dsn = sprintf("mysql:host=%s;charset=%s", $config['host'], $config['charset']);
            $tempConnection = new PDO(
                $dsn,
                $config['username'],
                $config['password'],
                [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
            );

            $tempConnection->exec("CREATE DATABASE IF NOT EXISTS " . $config['dbname'] . " CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
            $tempConnection = null;

        } catch (PDOException $e) {
            logError("Erreur lors de la création de la base de données: " . $e->getMessage());
            throw new Exception("Impossible de créer la base de données");
        }
    }

    /**
     * Retourne la connexion PDO
     */
    public function getConnection() {
        if ($this->connection === null) {
            $this->connect();
        }
        return $this->connection;
    }

    /**
     * Exécute une requête préparée avec gestion d'erreurs
     */
    public function executeQuery($sql, $params = []) {
        try {
            $stmt = $this->connection->prepare($sql);
            $stmt->execute($params);
            return $stmt;
        } catch (PDOException $e) {
            error_log("Erreur lors de l'exécution de la requête: " . $e->getMessage());
            error_log("SQL: " . $sql);
            error_log("Params: " . print_r($params, true));
            throw new Exception("Erreur lors de l'exécution de la requête");
        }
    }

    /**
     * Récupère tous les résultats d'une requête
     */
    public function fetchAll($sql, $params = []) {
        $stmt = $this->executeQuery($sql, $params);
        return $stmt->fetchAll();
    }

    /**
     * Récupère un seul résultat d'une requête
     */
    public function fetchOne($sql, $params = []) {
        $stmt = $this->executeQuery($sql, $params);
        return $stmt->fetch();
    }

    /**
     * Récupère une seule valeur d'une requête
     */
    public function fetchColumn($sql, $params = []) {
        $stmt = $this->executeQuery($sql, $params);
        return $stmt->fetchColumn();
    }

    /**
     * Insère des données et retourne l'ID du dernier élément inséré
     */
    public function insert($table, $data) {
        $columns = implode(', ', array_keys($data));
        $placeholders = ':' . implode(', :', array_keys($data));

        $sql = "INSERT INTO {$table} ({$columns}) VALUES ({$placeholders})";
        $this->executeQuery($sql, $data);

        return $this->connection->lastInsertId();
    }

    /**
     * Met à jour des données
     */
    public function update($table, $data, $where, $whereParams = []) {
        $setClause = [];
        foreach (array_keys($data) as $column) {
            $setClause[] = "{$column} = :{$column}";
        }
        $setClause = implode(', ', $setClause);

        $sql = "UPDATE {$table} SET {$setClause} WHERE {$where}";
        $params = array_merge($data, $whereParams);

        return $this->executeQuery($sql, $params);
    }

    /**
     * Supprime des données
     */
    public function delete($table, $where, $params = []) {
        $sql = "DELETE FROM {$table} WHERE {$where}";
        return $this->executeQuery($sql, $params);
    }

    /**
     * Démarre une transaction
     */
    public function beginTransaction() {
        return $this->connection->beginTransaction();
    }

    /**
     * Valide une transaction
     */
    public function commit() {
        return $this->connection->commit();
    }

    /**
     * Annule une transaction
     */
    public function rollback() {
        return $this->connection->rollback();
    }

    /**
     * Vérifie si une table existe
     */
    public function tableExists($tableName) {
        $sql = "SHOW TABLES LIKE :tableName";
        $result = $this->fetchOne($sql, ['tableName' => $tableName]);
        return !empty($result);
    }

    /**
     * Retourne les statistiques de la base de données
     */
    public function getDatabaseStats() {
        $stats = [];

        // Taille de la base de données
        $sql = "SELECT
                    ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS size_mb
                FROM information_schema.tables
                WHERE table_schema = :dbname";
        $stats['size_mb'] = $this->fetchColumn($sql, ['dbname' => self::DB_CONFIG['dbname']]);

        // Nombre de tables
        $sql = "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = :dbname";
        $stats['table_count'] = $this->fetchColumn($sql, ['dbname' => self::DB_CONFIG['dbname']]);

        return $stats;
    }
}

// Fonction helper pour obtenir l'instance de la base de données
function getDB() {
    return SchlutterDatabase::getInstance();
}

// Fonction helper pour obtenir la connexion PDO (compatibilité avec l'ancien code)
function getDbConnection() {
    return getDB()->getConnection();
}

// Variable globale pour compatibilité avec l'ancien code
$db = getDB()->getConnection();
