-- Script SQL pour créer les tables nécessaires au système d'évaluation des prestataires
-- Schluter Systems - Système d'évaluation

-- Table des évaluations (table principale)
CREATE TABLE IF NOT EXISTS `evaluations` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `prestataire` varchar(255) NOT NULL,
  `service` varchar(255) NOT NULL,
  `qualite` int(1) NOT NULL CHECK (`qualite` >= 1 AND `qualite` <= 5),
  `delai` int(1) NOT NULL CHECK (`delai` >= 1 AND `delai` <= 5),
  `communication` int(1) NOT NULL CHECK (`communication` >= 1 AND `communication` <= 5),
  `commentaires` text,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  INDEX `idx_prestataire` (`prestataire`),
  INDEX `idx_service` (`service`),
  INDEX `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table des prestataires (pour la gestion administrative)
CREATE TABLE IF NOT EXISTS `prestataires` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nom` varchar(255) NOT NULL UNIQUE,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_nom` (`nom`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table des services (pour la gestion administrative)
CREATE TABLE IF NOT EXISTS `services` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nom` varchar(255) NOT NULL UNIQUE,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_nom` (`nom`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table des notes des prestataires (système de notation séparé)
CREATE TABLE IF NOT EXISTS `provider_ratings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `prestataire` varchar(255) NOT NULL,
  `note` int(1) NOT NULL CHECK (`note` >= 1 AND `note` <= 5),
  `commentaire` text,
  `user_ip` varchar(45) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_user_provider` (`prestataire`, `user_ip`),
  INDEX `idx_prestataire` (`prestataire`),
  INDEX `idx_note` (`note`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insertion de quelques prestataires et services par défaut
INSERT IGNORE INTO `prestataires` (`nom`) VALUES
('Entreprise A'),
('Entreprise B'),
('Entreprise C'),
('Fournisseur Alpha'),
('Fournisseur Beta');

INSERT IGNORE INTO `services` (`nom`) VALUES
('Maintenance informatique'),
('Développement web'),
('Support technique'),
('Formation'),
('Consultation'),
('Installation matériel'),
('Réparation'),
('Nettoyage'),
('Sécurité'),
('Audit');

-- Vue pour les statistiques globales
CREATE OR REPLACE VIEW `evaluation_stats` AS
SELECT
    COUNT(*) as total_evaluations,
    AVG(qualite) as avg_qualite,
    AVG(delai) as avg_delai,
    AVG(communication) as avg_communication,
    COUNT(DISTINCT prestataire) as total_prestataires,
    COUNT(DISTINCT service) as total_services,
    AVG((qualite + delai + communication) / 3) as avg_global
FROM evaluations;

-- Vue pour le classement des prestataires
CREATE OR REPLACE VIEW `prestataire_ranking` AS
SELECT
    prestataire,
    COUNT(*) as nb_evaluations,
    AVG(qualite) as avg_qualite,
    AVG(delai) as avg_delai,
    AVG(communication) as avg_communication,
    AVG((qualite + delai + communication) / 3) as avg_global,
    MAX(created_at) as derniere_evaluation
FROM evaluations
GROUP BY prestataire
ORDER BY avg_global DESC, nb_evaluations DESC;

-- Vue pour les évaluations récentes avec détails
CREATE OR REPLACE VIEW `evaluations_recentes` AS
SELECT
    e.id,
    e.prestataire,
    e.service,
    e.qualite,
    e.delai,
    e.communication,
    e.commentaires,
    e.created_at,
    (e.qualite + e.delai + e.communication) / 3 as note_globale
FROM evaluations e
ORDER BY e.created_at DESC;
