<?php
// Démarrer la capture de sortie pour éviter les sorties non désirées
ob_start();

require_once __DIR__ . '/config/database.php';

// Créer les tables si elles n'existent pas
try {
    // Table des prestataires pour les évaluations
    $db->exec("CREATE TABLE IF NOT EXISTS evaluation_prestataires (
        id INT AUTO_INCREMENT PRIMARY KEY,
        nom VARCHAR(255) NOT NULL UNIQUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )");

    // Table des services pour les évaluations (différente de la table services existante)
    $db->exec("CREATE TABLE IF NOT EXISTS evaluation_services (
        id INT AUTO_INCREMENT PRIMARY KEY,
        nom VARCHAR(255) NOT NULL UNIQUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )");

    // Vérifier si la table evaluations existe et la créer si nécessaire
    $db->exec("CREATE TABLE IF NOT EXISTS evaluations (
        id INT AUTO_INCREMENT PRIMARY KEY,
        prestataire VARCHAR(255) NOT NULL,
        service VARCHAR(255) NOT NULL,
        qualite INT NOT NULL CHECK (qualite >= 1 AND qualite <= 5),
        delai INT NOT NULL CHECK (delai >= 1 AND delai <= 5),
        communication INT NOT NULL CHECK (communication >= 1 AND communication <= 5),
        commentaires TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_prestataire (prestataire),
        INDEX idx_service (service),
        INDEX idx_created_at (created_at)
    )");

    // Les prestataires et services seront ajoutés manuellement via l'interface d'administration

} catch (PDOException $e) {
    error_log("Erreur lors de la création des tables: " . $e->getMessage());
    // Arrêter l'exécution si les tables critiques ne peuvent pas être créées
    die("Erreur critique: Impossible de créer les tables de base de données. Veuillez contacter l'administrateur.");
}

// Function to handle database operations
function handleDb($action, $data = null) {
    global $db;

    switch ($action) {
        case "read":
            $stmt = $db->query("SELECT * FROM evaluations ORDER BY created_at DESC");
            return $stmt->fetchAll(PDO::FETCH_ASSOC);

        case "write":
            $stmt = $db->prepare("INSERT INTO evaluations (prestataire, service, qualite, delai, communication, commentaires) VALUES (?, ?, ?, ?, ?, ?)");
            return $stmt->execute($data);

        case "getProviders":
            // Récupérer depuis la table evaluation_prestataires d'abord, puis depuis evaluations pour compatibilité
            $stmt = $db->query("SELECT nom FROM evaluation_prestataires ORDER BY nom");
            $providers = $stmt->fetchAll(PDO::FETCH_COLUMN);

            // Ajouter les prestataires qui pourraient être uniquement dans evaluations
            $stmt = $db->query("SELECT DISTINCT prestataire FROM evaluations WHERE prestataire NOT IN (SELECT nom FROM evaluation_prestataires) ORDER BY prestataire");
            $additionalProviders = $stmt->fetchAll(PDO::FETCH_COLUMN);

            return array_merge($providers, $additionalProviders);

        case "getServices":
            // Récupérer depuis la table evaluation_services d'abord, puis depuis evaluations pour compatibilité
            $stmt = $db->query("SELECT nom FROM evaluation_services ORDER BY nom");
            $services = $stmt->fetchAll(PDO::FETCH_COLUMN);

            // Ajouter les services qui pourraient être uniquement dans evaluations
            $stmt = $db->query("SELECT DISTINCT service FROM evaluations WHERE service NOT IN (SELECT nom FROM evaluation_services) ORDER BY service");
            $additionalServices = $stmt->fetchAll(PDO::FETCH_COLUMN);

            return array_merge($services, $additionalServices);

        case "addProvider":
            $stmt = $db->prepare("INSERT INTO evaluation_prestataires (nom) VALUES (?)");
            return $stmt->execute([$data]);

        case "removeProvider":
            // Supprimer le prestataire et toutes ses évaluations
            if (empty($data)) {
                throw new Exception("Nom du prestataire vide");
            }

            $db->beginTransaction();
            try {
                // Vérifier si le prestataire existe
                $stmt = $db->prepare("SELECT COUNT(*) as count FROM evaluation_prestataires WHERE nom = ?");
                $stmt->execute([$data]);
                $count = $stmt->fetch()['count'];

                if ($count == 0) {
                    $db->rollback();
                    throw new Exception("Prestataire '$data' non trouvé");
                }

                // Supprimer les évaluations associées
                $stmt = $db->prepare("DELETE FROM evaluations WHERE prestataire = ?");
                $stmt->execute([$data]);
                $deletedEvaluations = $stmt->rowCount();

                // Supprimer le prestataire
                $stmt = $db->prepare("DELETE FROM evaluation_prestataires WHERE nom = ?");
                $result = $stmt->execute([$data]);
                $deletedProviders = $stmt->rowCount();

                if ($deletedProviders == 0) {
                    $db->rollback();
                    throw new Exception("Impossible de supprimer le prestataire '$data'");
                }

                $db->commit();
                return [
                    'success' => true,
                    'deleted_evaluations' => $deletedEvaluations,
                    'deleted_providers' => $deletedProviders
                ];
            } catch (Exception $e) {
                $db->rollback();
                throw $e;
            }

        case "addService":
            $stmt = $db->prepare("INSERT INTO evaluation_services (nom) VALUES (?)");
            return $stmt->execute([$data]);

        case "removeService":
            // Supprimer le service et toutes les évaluations associées
            if (empty($data)) {
                throw new Exception("Nom du service vide");
            }

            $db->beginTransaction();
            try {
                // Vérifier si le service existe
                $stmt = $db->prepare("SELECT COUNT(*) as count FROM evaluation_services WHERE nom = ?");
                $stmt->execute([$data]);
                $count = $stmt->fetch()['count'];

                if ($count == 0) {
                    $db->rollback();
                    throw new Exception("Service '$data' non trouvé");
                }

                // Supprimer les évaluations associées
                $stmt = $db->prepare("DELETE FROM evaluations WHERE service = ?");
                $stmt->execute([$data]);
                $deletedEvaluations = $stmt->rowCount();

                // Supprimer le service
                $stmt = $db->prepare("DELETE FROM evaluation_services WHERE nom = ?");
                $result = $stmt->execute([$data]);
                $deletedServices = $stmt->rowCount();

                if ($deletedServices == 0) {
                    $db->rollback();
                    throw new Exception("Impossible de supprimer le service '$data'");
                }

                $db->commit();
                return [
                    'success' => true,
                    'deleted_evaluations' => $deletedEvaluations,
                    'deleted_services' => $deletedServices
                ];
            } catch (Exception $e) {
                $db->rollback();
                throw $e;
            }
    }
    return null;
}

// Handle AJAX requests for data updates
if ($_SERVER["REQUEST_METHOD"] == "GET" && isset($_GET['action'])) {
    header('Content-Type: application/json');

    switch ($_GET['action']) {
        case 'getProviders':
            $providers = handleDb("getProviders");
            echo json_encode(['providers' => $providers]);
            exit();

        case 'getServices':
            $services = handleDb("getServices");
            echo json_encode(['services' => $services]);
            exit();
    }
}

// Handle form submissions
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Nettoyer toute sortie précédente
    ob_clean();

    // S'assurer que la réponse sera en JSON
    header('Content-Type: application/json');
    header('Cache-Control: no-cache, must-revalidate');

    $response = ['status' => 'success'];

    try {
        // Gestion des évaluations
        if (isset($_POST['provider']) && !isset($_POST['action'])) {
            if (isset($_POST['provider'], $_POST['serviceType'], $_POST['serviceQuality'],
                $_POST['deliveryTime'], $_POST['communication'])) {
                $data = [
                    $_POST['provider'],
                    $_POST['serviceType'],
                    intval($_POST['serviceQuality']),
                    intval($_POST['deliveryTime']),
                    intval($_POST['communication']),
                    $_POST['comments'] ?? ''
                ];
                handleDb("write", $data);
                $response['message'] = 'Évaluation soumise avec succès!';
            } else {
                $response = ['status' => 'error', 'message' => 'Données d\'évaluation incomplètes.'];
                http_response_code(400);
            }
        }
        // Gestion des actions d'administration
        elseif (isset($_POST['action'], $_POST['newItem'])) {
            $action = $_POST['action'];
            $item = trim($_POST['newItem']);

            if (empty($item)) {
                $response = ['status' => 'error', 'message' => 'Le nom ne peut pas être vide.'];
                http_response_code(400);
            } else {
                switch ($action) {
                    case 'addProvider':
                        handleDb("addProvider", $item);
                        $response['message'] = 'Prestataire ajouté avec succès!';
                        break;
                    case 'addService':
                        handleDb("addService", $item);
                        $response['message'] = 'Service ajouté avec succès!';
                        break;
                    default:
                        $response = ['status' => 'error', 'message' => 'Action non reconnue.'];
                        http_response_code(400);
                }
            }
        }
        // Gestion de la suppression
        elseif (isset($_POST['action'], $_POST['removeItem'])) {
            $action = $_POST['action'];
            $item = trim($_POST['removeItem']);

            if (empty($item)) {
                $response = ['status' => 'error', 'message' => 'Veuillez sélectionner un élément à supprimer.'];
                http_response_code(400);
            } else {
                switch ($action) {
                    case 'removeProvider':
                        $result = handleDb("removeProvider", $item);
                        if (is_array($result) && $result['success']) {
                            $response['message'] = "Prestataire '$item' supprimé avec succès! ({$result['deleted_evaluations']} évaluations supprimées)";
                        } else {
                            $response['message'] = 'Prestataire supprimé avec succès!';
                        }
                        break;
                    case 'removeService':
                        $result = handleDb("removeService", $item);
                        if (is_array($result) && $result['success']) {
                            $response['message'] = "Service '$item' supprimé avec succès! ({$result['deleted_evaluations']} évaluations supprimées)";
                        } else {
                            $response['message'] = 'Service supprimé avec succès!';
                        }
                        break;
                    default:
                        $response = ['status' => 'error', 'message' => 'Action non reconnue.'];
                        http_response_code(400);
                }
            }
        } else {
            $response = ['status' => 'error', 'message' => 'Données de formulaire invalides.'];
            http_response_code(400);
        }

    } catch (PDOException $e) {
        $response = ['status' => 'error', 'message' => 'Erreur de base de données: ' . $e->getMessage()];
        http_response_code(500);
    } catch (Exception $e) {
        $response = ['status' => 'error', 'message' => 'Erreur: ' . $e->getMessage()];
        http_response_code(500);
    }

    // S'assurer qu'il n'y a pas de sortie supplémentaire
    ob_clean();

    // Encoder et envoyer la réponse JSON
    $jsonResponse = json_encode($response, JSON_UNESCAPED_UNICODE);

    if ($jsonResponse === false) {
        // Si l'encodage JSON échoue, envoyer une réponse d'erreur simple
        http_response_code(500);
        echo '{"status":"error","message":"Erreur d\'encodage JSON"}';
    } else {
        echo $jsonResponse;
    }

    exit();
}

// Charger les prestataires et services depuis la base de données
$providers = handleDb("getProviders");
$services = handleDb("getServices");

// Nettoyer la sortie pour l'affichage HTML
ob_end_clean();
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Évaluation des Prestataires - Schluter Systems</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'schluter-orange': '#f57c00',
                        'schluter-blue': '#005ea2',
                        'schluter-gray': '#f8f9fa'
                    }
                }
            }
        }
    </script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

        body {
            font-family: 'Inter', sans-serif;
        }

        .card-hover {
            transition: all 0.3s ease;
        }

        .card-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        .input-focus {
            transition: all 0.3s ease;
        }

        .input-focus:focus {
            transform: scale(1.02);
            box-shadow: 0 0 0 3px rgba(245, 124, 0, 0.1);
        }

        .btn-gradient {
            background: linear-gradient(135deg, #f57c00 0%, #ff9800 100%);
            transition: all 0.3s ease;
        }

        .btn-gradient:hover {
            background: linear-gradient(135deg, #e67e22 0%, #f57c00 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(245, 124, 0, 0.3);
        }

        .popup-backdrop {
            backdrop-filter: blur(8px);
            background: rgba(0, 0, 0, 0.5);
        }

        .popup-content {
            animation: slideIn 0.3s ease-out;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translate(-50%, -60%);
            }
            to {
                opacity: 1;
                transform: translate(-50%, -50%);
            }
        }

        .rating-stars {
            color: #fbbf24;
        }

        .admin-icon {
            transition: all 0.3s ease;
        }

        .admin-icon:hover i {
            transform: rotate(90deg);
        }

        .admin-icon i {
            transition: transform 0.3s ease;
        }
    </style>
</head>
<body>
    <?php include 'top-bar-eval.php'; ?>

    <!-- Background Pattern -->
    <div class="min-h-screen bg-gradient-to-br from-schluter-gray via-white to-blue-50 py-8">
        <div class="container mx-auto px-4 max-w-4xl">

            <!-- Header Section -->
            <div class="text-center mb-8 evaluation-header">
                <div class="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-schluter-orange to-yellow-500 rounded-full mb-4 shadow-lg">
                    <i class="fas fa-star text-white text-2xl"></i>
                </div>
                <h1 class="text-4xl font-bold text-gray-800 mb-2">Évaluation des Prestataires</h1>
                <p class="text-gray-600 text-lg">Partagez votre expérience pour améliorer nos services</p>
            </div>

            <!-- Main Card -->
            <div class="bg-white rounded-2xl shadow-xl card-hover p-8 mb-8 evaluation-card">
                <form id="evaluationForm" class="space-y-6">

                    <!-- Provider Selection -->
                    <div class="form-group">
                        <label for="provider" class="flex items-center text-sm font-semibold text-gray-700 mb-3">
                            <i class="fas fa-building text-schluter-orange mr-2"></i>
                            Prestataire évalué
                        </label>
                        <select id="provider" name="provider" required class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:border-schluter-orange focus:ring-0 input-focus transition-all duration-300">
                            <option value="">Sélectionnez un prestataire</option>
                            <?php foreach ($providers as $provider): ?>
                                <option value="<?= htmlspecialchars($provider) ?>"><?= htmlspecialchars($provider) ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <!-- Service Selection -->
                    <div class="form-group">
                        <label for="serviceType" class="flex items-center text-sm font-semibold text-gray-700 mb-3">
                            <i class="fas fa-tools text-schluter-orange mr-2"></i>
                            Service demandé
                        </label>
                        <select id="serviceType" name="serviceType" required class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:border-schluter-orange focus:ring-0 input-focus transition-all duration-300">
                            <option value="">Sélectionnez un service</option>
                            <?php foreach ($services as $service): ?>
                                <option value="<?= htmlspecialchars($service) ?>"><?= htmlspecialchars($service) ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <!-- Rating Grid -->
                    <div class="grid md:grid-cols-3 gap-6">

                        <!-- Quality Rating -->
                        <div class="form-group">
                            <label for="serviceQuality" class="flex items-center text-sm font-semibold text-gray-700 mb-3">
                                <i class="fas fa-gem text-schluter-orange mr-2"></i>
                                Qualité du service
                            </label>
                            <select id="serviceQuality" name="serviceQuality" required class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:border-schluter-orange focus:ring-0 input-focus transition-all duration-300">
                                <option value="">Note</option>
                                <option value="5">⭐⭐⭐⭐⭐ Excellent</option>
                                <option value="4">⭐⭐⭐⭐ Très bon</option>
                                <option value="3">⭐⭐⭐ Bon</option>
                                <option value="2">⭐⭐ Moyen</option>
                                <option value="1">⭐ Mauvais</option>
                            </select>
                        </div>

                        <!-- Delivery Time Rating -->
                        <div class="form-group">
                            <label for="deliveryTime" class="flex items-center text-sm font-semibold text-gray-700 mb-3">
                                <i class="fas fa-clock text-schluter-orange mr-2"></i>
                                Délai de livraison
                            </label>
                            <select id="deliveryTime" name="deliveryTime" required class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:border-schluter-orange focus:ring-0 input-focus transition-all duration-300">
                                <option value="">Note</option>
                                <option value="5">⭐⭐⭐⭐⭐ Très rapide</option>
                                <option value="4">⭐⭐⭐⭐ Rapide</option>
                                <option value="3">⭐⭐⭐ Moyen</option>
                                <option value="2">⭐⭐ Lent</option>
                                <option value="1">⭐ Très lent</option>
                            </select>
                        </div>

                        <!-- Communication Rating -->
                        <div class="form-group">
                            <label for="communication" class="flex items-center text-sm font-semibold text-gray-700 mb-3">
                                <i class="fas fa-comments text-schluter-orange mr-2"></i>
                                Communication
                            </label>
                            <select id="communication" name="communication" required class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:border-schluter-orange focus:ring-0 input-focus transition-all duration-300">
                                <option value="">Note</option>
                                <option value="5">⭐⭐⭐⭐⭐ Excellente</option>
                                <option value="4">⭐⭐⭐⭐ Très bonne</option>
                                <option value="3">⭐⭐⭐ Bonne</option>
                                <option value="2">⭐⭐ Moyenne</option>
                                <option value="1">⭐ Mauvaise</option>
                            </select>
                        </div>
                    </div>

                    <!-- Comments Section -->
                    <div class="form-group">
                        <label for="comments" class="flex items-center text-sm font-semibold text-gray-700 mb-3">
                            <i class="fas fa-edit text-schluter-orange mr-2"></i>
                            Commentaires additionnels
                        </label>
                        <textarea id="comments" name="comments" rows="4"
                                placeholder="Partagez vos commentaires détaillés sur votre expérience..."
                                class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:border-schluter-orange focus:ring-0 input-focus transition-all duration-300 resize-none"></textarea>
                    </div>

                    <!-- Submit Button -->
                    <div class="pt-4">
                        <button type="submit" class="w-full btn-gradient text-white font-semibold py-4 px-8 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center space-x-2">
                            <i class="fas fa-paper-plane"></i>
                            <span>Soumettre l'évaluation</span>
                        </button>
                    </div>
                </form>
            </div>

            <!-- Action Buttons -->
            <div class="flex justify-between items-center">
                <button onclick="window.location.href='evaluation-des-prestataires.php'"
                        class="flex items-center space-x-2 px-6 py-3 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-xl transition-all duration-300 shadow-md hover:shadow-lg">
                    <i class="fas fa-arrow-left"></i>
                    <span>Retour</span>
                </button>

                <button onclick="togglePopup()"
                        class="admin-icon flex items-center space-x-2 px-6 py-3 bg-schluter-blue hover:bg-blue-700 text-white rounded-xl transition-all duration-300 shadow-md hover:shadow-lg">
                    <i class="fas fa-cogs"></i>
                    <span>Administration</span>
                </button>
            </div>
        </div>
    </div>
    <!-- Admin Popup -->
    <div id="adminPopup" class="hidden fixed inset-0 popup-backdrop z-50 flex items-center justify-center p-4" style="padding-top: 40px; padding-bottom: 40px;">
        <div class="popup-content bg-white rounded-2xl shadow-2xl max-w-2xl w-full max-h-[75vh] overflow-y-auto mx-auto my-auto">

            <!-- Header -->
            <div class="bg-gradient-to-r from-schluter-orange to-yellow-500 text-white p-6 rounded-t-2xl">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <i class="fas fa-cogs text-2xl"></i>
                        <h2 class="text-2xl font-bold">Gestion des Prestataires et Services</h2>
                    </div>
                    <button onclick="togglePopup()" class="text-white hover:text-gray-200 transition-colors">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
            </div>

            <div class="p-6 space-y-8">

                <!-- Providers Section -->
                <div class="space-y-6">
                    <h3 class="text-xl font-semibold text-gray-800 flex items-center">
                        <i class="fas fa-building text-schluter-orange mr-2"></i>
                        Gestion des Prestataires
                    </h3>

                    <!-- Add Provider -->
                    <div class="bg-green-50 border border-green-200 rounded-xl p-4">
                        <form id="addProviderForm" method="POST" class="space-y-4">
                            <input type="hidden" name="action" value="addProvider">
                            <label class="block text-sm font-semibold text-gray-700">
                                <i class="fas fa-plus text-green-600 mr-2"></i>
                                Ajouter un prestataire
                            </label>
                            <div class="flex space-x-3">
                                <input type="text" id="newProvider" name="newItem"
                                       placeholder="Nom du prestataire" required
                                       class="flex-1 px-4 py-3 border-2 border-gray-200 rounded-xl focus:border-green-500 focus:ring-0 transition-all duration-300">
                                <button type="submit" class="px-6 py-3 bg-green-600 hover:bg-green-700 text-white rounded-xl transition-all duration-300 flex items-center space-x-2">
                                    <i class="fas fa-plus"></i>
                                    <span>Ajouter</span>
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- Remove Provider -->
                    <div class="bg-red-50 border border-red-200 rounded-xl p-4">
                        <form id="removeProviderForm" method="POST" class="space-y-4">
                            <input type="hidden" name="action" value="removeProvider">
                            <label class="block text-sm font-semibold text-gray-700">
                                <i class="fas fa-trash text-red-600 mr-2"></i>
                                Supprimer un prestataire
                            </label>
                            <div class="flex space-x-3">
                                <select id="removeProvider" name="removeItem" required
                                        class="flex-1 px-4 py-3 border-2 border-gray-200 rounded-xl focus:border-red-500 focus:ring-0 transition-all duration-300">
                                    <option value="">Sélectionnez un prestataire</option>
                                    <?php foreach ($providers as $provider): ?>
                                        <option value="<?= htmlspecialchars($provider) ?>"><?= htmlspecialchars($provider) ?></option>
                                    <?php endforeach; ?>
                                </select>
                                <button type="submit" class="px-6 py-3 bg-red-600 hover:bg-red-700 text-white rounded-xl transition-all duration-300 flex items-center space-x-2">
                                    <i class="fas fa-trash"></i>
                                    <span>Supprimer</span>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Services Section -->
                <div class="space-y-6">
                    <h3 class="text-xl font-semibold text-gray-800 flex items-center">
                        <i class="fas fa-tools text-schluter-orange mr-2"></i>
                        Gestion des Services
                    </h3>

                    <!-- Add Service -->
                    <div class="bg-blue-50 border border-blue-200 rounded-xl p-4">
                        <form id="addServiceForm" method="POST" class="space-y-4">
                            <input type="hidden" name="action" value="addService">
                            <label class="block text-sm font-semibold text-gray-700">
                                <i class="fas fa-plus text-blue-600 mr-2"></i>
                                Ajouter un service
                            </label>
                            <div class="flex space-x-3">
                                <input type="text" id="newService" name="newItem"
                                       placeholder="Nom du service" required
                                       class="flex-1 px-4 py-3 border-2 border-gray-200 rounded-xl focus:border-blue-500 focus:ring-0 transition-all duration-300">
                                <button type="submit" class="px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-xl transition-all duration-300 flex items-center space-x-2">
                                    <i class="fas fa-plus"></i>
                                    <span>Ajouter</span>
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- Remove Service -->
                    <div class="bg-orange-50 border border-orange-200 rounded-xl p-4">
                        <form id="removeServiceForm" method="POST" class="space-y-4">
                            <input type="hidden" name="action" value="removeService">
                            <label class="block text-sm font-semibold text-gray-700">
                                <i class="fas fa-trash text-orange-600 mr-2"></i>
                                Supprimer un service
                            </label>
                            <div class="flex space-x-3">
                                <select id="removeService" name="removeItem" required
                                        class="flex-1 px-4 py-3 border-2 border-gray-200 rounded-xl focus:border-orange-500 focus:ring-0 transition-all duration-300">
                                    <option value="">Sélectionnez un service</option>
                                    <?php foreach ($services as $service): ?>
                                        <option value="<?= htmlspecialchars($service) ?>"><?= htmlspecialchars($service) ?></option>
                                    <?php endforeach; ?>
                                </select>
                                <button type="submit" class="px-6 py-3 bg-orange-600 hover:bg-orange-700 text-white rounded-xl transition-all duration-300 flex items-center space-x-2">
                                    <i class="fas fa-trash"></i>
                                    <span>Supprimer</span>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Close Button -->
                <div class="pt-4 border-t border-gray-200">
                    <button onclick="togglePopup()" class="w-full px-6 py-3 bg-gray-600 hover:bg-gray-700 text-white rounded-xl transition-all duration-300 flex items-center justify-center space-x-2">
                        <i class="fas fa-times"></i>
                        <span>Fermer</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Fonction de langue par défaut (pour éviter les erreurs)
        function changeLanguage(lang) {
            // Fonction vide pour éviter les erreurs
            console.log('Langue sélectionnée:', lang);
        }

        document.addEventListener('DOMContentLoaded', () => {
            // Initialiser la langue par défaut
            changeLanguage(localStorage.getItem('language') || 'fr');

            document.getElementById('evaluationForm').addEventListener('submit', handleFormSubmit);
            document.getElementById('addProviderForm').addEventListener('submit', handleFormSubmit);
            document.getElementById('removeProviderForm').addEventListener('submit', handleFormSubmit);
            document.getElementById('addServiceForm').addEventListener('submit', handleFormSubmit);
            document.getElementById('removeServiceForm').addEventListener('submit', handleFormSubmit);
        });

        function handleFormSubmit(event) {
            event.preventDefault();
            const form = event.target;
            const formData = new FormData(form);

            // Afficher un indicateur de chargement
            const submitBtn = form.querySelector('button[type="submit"]');
            const originalText = submitBtn.textContent;
            submitBtn.textContent = 'Traitement...';
            submitBtn.disabled = true;

            fetch('evaluation.php', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                console.log('Response status:', response.status);
                console.log('Response headers:', response.headers);

                // Vérifier si la réponse est OK
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                // Vérifier le type de contenu
                const contentType = response.headers.get('content-type');
                if (!contentType || !contentType.includes('application/json')) {
                    // Si ce n'est pas du JSON, lire comme texte pour voir l'erreur
                    return response.text().then(text => {
                        console.error('Response is not JSON:', text);
                        throw new Error('La réponse du serveur n\'est pas au format JSON. Contenu: ' + text.substring(0, 200));
                    });
                }

                return response.json();
            })
            .then(data => {
                console.log('Response data:', data);

                if (data.status === 'success') {
                    // Succès silencieux - pas de popup

                    // Si c'est le formulaire d'évaluation principal, recharger la page
                    if (form.id === 'evaluationForm') {
                        location.reload();
                    } else {
                        // Pour les actions d'administration, garder la popup ouverte et recharger seulement les listes
                        showLoadingMessage();
                        reloadAdminData();
                        // Réinitialiser le formulaire
                        form.reset();
                    }
                } else {
                    // Afficher les erreurs avec plus de détails
                    console.error('Erreur détaillée:', data);
                    alert('Erreur: ' + (data.message || 'Une erreur est survenue.'));
                }
            })
            .catch(error => {
                console.error('Error details:', error);
                alert('Erreur de communication avec le serveur: ' + error.message);
            })
            .finally(() => {
                // Restaurer le bouton
                submitBtn.textContent = originalText;
                submitBtn.disabled = false;
            });
        }

        // Fonction pour afficher un message de chargement
        function showLoadingMessage() {
            const popup = document.getElementById('adminPopup');
            const existingMessage = popup.querySelector('.loading-message');

            if (!existingMessage) {
                const loadingDiv = document.createElement('div');
                loadingDiv.className = 'loading-message';
                loadingDiv.style.cssText = `
                    position: absolute;
                    top: 10px;
                    right: 10px;
                    background-color: #f57c00;
                    color: white;
                    padding: 5px 10px;
                    border-radius: 4px;
                    font-size: 12px;
                    z-index: 1001;
                `;
                loadingDiv.textContent = 'Mise à jour des listes...';
                popup.appendChild(loadingDiv);

                // Supprimer le message après 2 secondes
                setTimeout(() => {
                    if (loadingDiv.parentNode) {
                        loadingDiv.parentNode.removeChild(loadingDiv);
                    }
                }, 2000);
            }
        }

        // Fonction pour recharger les données d'administration sans fermer la popup
        function reloadAdminData() {
            let providersLoaded = false;
            let servicesLoaded = false;

            // Recharger les listes déroulantes dans le formulaire principal
            fetch('evaluation.php?action=getProviders')
                .then(response => response.json())
                .then(data => {
                    if (data.providers) {
                        updateProviderSelect(data.providers);
                        updatePopupProviderSelect(data.providers);
                        providersLoaded = true;
                        checkLoadingComplete();
                    }
                })
                .catch(error => {
                    console.error('Erreur lors du rechargement des prestataires:', error);
                    providersLoaded = true;
                    checkLoadingComplete();
                });

            fetch('evaluation.php?action=getServices')
                .then(response => response.json())
                .then(data => {
                    if (data.services) {
                        updateServiceSelect(data.services);
                        updatePopupServiceSelect(data.services);
                        servicesLoaded = true;
                        checkLoadingComplete();
                    }
                })
                .catch(error => {
                    console.error('Erreur lors du rechargement des services:', error);
                    servicesLoaded = true;
                    checkLoadingComplete();
                });

            function checkLoadingComplete() {
                if (providersLoaded && servicesLoaded) {
                    // Supprimer le message de chargement s'il existe encore
                    const loadingMessage = document.querySelector('.loading-message');
                    if (loadingMessage) {
                        loadingMessage.remove();
                    }
                }
            }
        }

        // Fonction pour mettre à jour la liste des prestataires dans le formulaire principal
        function updateProviderSelect(providers) {
            const providerSelect = document.getElementById('provider');

            // Sauvegarder la valeur actuelle
            const currentValue = providerSelect.value;

            // Vider et repeupler
            providerSelect.innerHTML = '<option value="">Sélectionnez un prestataire</option>';

            providers.forEach(provider => {
                const option = new Option(provider, provider);
                providerSelect.add(option);
            });

            // Restaurer la valeur si elle existe encore
            if (providers.includes(currentValue)) {
                providerSelect.value = currentValue;
            }
        }

        // Fonction pour mettre à jour la liste des services
        function updateServiceSelect(services) {
            const serviceSelect = document.getElementById('serviceType');

            // Sauvegarder la valeur actuelle
            const currentValue = serviceSelect.value;

            // Vider et repeupler
            serviceSelect.innerHTML = '<option value="">Sélectionnez un service</option>';

            services.forEach(service => {
                const option = new Option(service, service);
                serviceSelect.add(option);
            });

            // Restaurer la valeur si elle existe encore
            if (services.includes(currentValue)) {
                serviceSelect.value = currentValue;
            }
        }

        // Fonction pour mettre à jour la liste des prestataires dans la popup
        function updatePopupProviderSelect(providers) {
            const removeProviderSelect = document.getElementById('removeProvider');

            // Vider et repeupler
            removeProviderSelect.innerHTML = '<option value="">Sélectionnez un prestataire</option>';

            providers.forEach(provider => {
                const option = new Option(provider, provider);
                removeProviderSelect.add(option);
            });
        }

        // Fonction pour mettre à jour la liste des services dans la popup
        function updatePopupServiceSelect(services) {
            const removeServiceSelect = document.getElementById('removeService');

            // Vider et repeupler
            removeServiceSelect.innerHTML = '<option value="">Sélectionnez un service</option>';

            services.forEach(service => {
                const option = new Option(service, service);
                removeServiceSelect.add(option);
            });
        }

        function togglePopup() {
            const popup = document.getElementById('adminPopup');
            popup.classList.toggle('hidden');

            // Animation d'entrée avec GSAP
            if (!popup.classList.contains('hidden')) {
                gsap.fromTo(popup.querySelector('.popup-content'),
                    { scale: 0.8, opacity: 0, y: -50 },
                    { scale: 1, opacity: 1, y: 0, duration: 0.4, ease: "back.out(1.7)" }
                );
            }
        }

        // Initialisation des animations GSAP
        document.addEventListener('DOMContentLoaded', () => {
            // Animation d'entrée pour le header
            gsap.fromTo('.evaluation-header',
                { y: -50, opacity: 0 },
                { y: 0, opacity: 1, duration: 0.8, ease: "power2.out" }
            );

            // Animation d'entrée pour la carte principale
            gsap.fromTo('.evaluation-card',
                { y: 50, opacity: 0 },
                { y: 0, opacity: 1, duration: 0.8, delay: 0.2, ease: "power2.out" }
            );

            // Animation pour les champs de formulaire
            gsap.fromTo('.form-group',
                { x: -30, opacity: 0 },
                { x: 0, opacity: 1, duration: 0.6, stagger: 0.1, delay: 0.4, ease: "power2.out" }
            );
        });
    </script>
</body>
</html>
