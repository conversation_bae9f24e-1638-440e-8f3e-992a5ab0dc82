<?php
/**
 * Composants réutilisables pour le portail Schluter Systems
 * Utilise la configuration centralisée et Tailwind CSS
 */

require_once __DIR__ . '/../config/schluter-config.php';

/**
 * Génère le header HTML standardisé
 */
function renderSchlutterHeader($title = 'Schluter Systems', $description = '', $additionalAssets = []) {
    echo "<!DOCTYPE html>\n<html lang=\"fr\">\n<head>\n";
    echo generateSchlutterHead($title, $description, $additionalAssets);
    echo "\n</head>\n<body class=\"bg-schluter-background font-sans\">\n";
}

/**
 * Génère la barre de navigation principale
 */
function renderSchlutterNavigation($currentPage = '', $moduleType = 'main') {
    $modules = SCHLUTER_MODULES;
    $currentPageBase = basename($currentPage);

    echo '<header class="fixed top-0 left-0 right-0 z-50 bg-white shadow-schluter-lg">';
    echo '<nav class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">';
    echo '<div class="flex justify-between items-center h-16">';

    // Logo et titre
    echo '<div class="flex items-center space-x-4">';
    echo '<a href="index.php" class="flex items-center space-x-3 hover-scale transition-transform duration-200">';
    echo '<img src="img/logo_rgb.svg" alt="Schluter Systems Logo" class="h-10 w-auto">';
    echo '<span class="text-xl font-bold text-schluter-gray">Schluter Systems</span>';
    echo '</a>';
    echo '</div>';

    // Navigation principale
    echo '<div class="hidden md:flex items-center space-x-1">';

    foreach ($modules as $key => $module) {
        $isActive = ($currentPageBase === basename($module['url']));
        $activeClass = $isActive ? 'bg-schluter-orange text-white' : 'text-schluter-gray hover:bg-schluter-orange hover:text-white';

        echo "<a href=\"{$module['url']}\" class=\"{$activeClass} px-4 py-2 rounded-lg font-medium transition-all duration-200 flex items-center space-x-2\">";
        echo "<i class=\"{$module['icon']}\"></i>";
        echo "<span>{$module['name']}</span>";
        echo "</a>";
    }

    // Bouton de déconnexion
    echo '<a href="login.php?logout=true" class="text-schluter-danger hover:bg-schluter-danger hover:text-white px-4 py-2 rounded-lg font-medium transition-all duration-200 flex items-center space-x-2">';
    echo '<i class="fas fa-sign-out-alt"></i>';
    echo '<span>Déconnexion</span>';
    echo '</a>';

    echo '</div>';

    // Menu mobile (hamburger)
    echo '<div class="md:hidden">';
    echo '<button id="mobile-menu-button" class="text-schluter-gray hover:text-schluter-orange p-2 rounded-lg transition-colors duration-200">';
    echo '<i class="fas fa-bars text-xl"></i>';
    echo '</button>';
    echo '</div>';

    echo '</div>';

    // Menu mobile
    echo '<div id="mobile-menu" class="hidden md:hidden bg-white border-t border-gray-200">';
    echo '<div class="px-2 pt-2 pb-3 space-y-1">';

    foreach ($modules as $key => $module) {
        $isActive = ($currentPageBase === basename($module['url']));
        $activeClass = $isActive ? 'bg-schluter-orange text-white' : 'text-schluter-gray hover:bg-schluter-orange hover:text-white';

        echo "<a href=\"{$module['url']}\" class=\"{$activeClass} block px-3 py-2 rounded-lg font-medium transition-all duration-200\">";
        echo "<i class=\"{$module['icon']} mr-2\"></i>";
        echo "{$module['name']}";
        echo "</a>";
    }

    echo '<a href="login.php?logout=true" class="text-schluter-danger hover:bg-schluter-danger hover:text-white block px-3 py-2 rounded-lg font-medium transition-all duration-200">';
    echo '<i class="fas fa-sign-out-alt mr-2"></i>';
    echo 'Déconnexion';
    echo '</a>';

    echo '</div>';
    echo '</div>';

    echo '</nav>';
    echo '</header>';

    // Spacer pour le header fixe
    echo '<div class="h-16"></div>';

    // Script pour le menu mobile
    echo '<script>
    document.getElementById("mobile-menu-button").addEventListener("click", function() {
        const menu = document.getElementById("mobile-menu");
        menu.classList.toggle("hidden");
    });
    </script>';
}

/**
 * Génère une navigation spécialisée pour les sous-modules
 */
function renderSchlutterSubNavigation($moduleKey, $currentPage = '') {
    $modules = SCHLUTER_MODULES;

    if (!isset($modules[$moduleKey]) || !isset($modules[$moduleKey]['submodules'])) {
        return;
    }

    $module = $modules[$moduleKey];
    $submodules = $module['submodules'];
    $currentPageBase = basename($currentPage);

    echo '<div class="bg-white shadow-schluter mb-6">';
    echo '<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">';
    echo '<div class="flex items-center justify-between h-14">';

    // Titre du module
    echo '<div class="flex items-center space-x-3">';
    echo "<i class=\"{$module['icon']} text-schluter-orange text-xl\"></i>";
    echo "<h2 class=\"text-lg font-semibold text-schluter-gray\">{$module['name']}</h2>";
    echo '</div>';

    // Navigation des sous-modules
    echo '<div class="flex items-center space-x-1">';

    foreach ($submodules as $key => $submodule) {
        $isActive = ($currentPageBase === basename($submodule['url']));
        $activeClass = $isActive ? 'bg-schluter-blue text-white' : 'text-schluter-gray hover:bg-schluter-blue hover:text-white';

        echo "<a href=\"{$submodule['url']}\" class=\"{$activeClass} px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center space-x-2\">";
        echo "<i class=\"{$submodule['icon']}\"></i>";
        echo "<span>{$submodule['name']}</span>";
        echo "</a>";
    }

    echo '</div>';
    echo '</div>';
    echo '</div>';
    echo '</div>';
}

/**
 * Génère une bannière de page avec gradient
 */
function renderSchlutterBanner($title, $subtitle = '', $additionalContent = '') {
    echo '<div class="gradient-schluter py-16 px-4 sm:px-6 lg:px-8 text-white animate-fade-in">';
    echo '<div class="max-w-7xl mx-auto text-center">';
    echo "<h1 class=\"text-4xl md:text-5xl font-bold mb-4 animate-slide-up\">{$title}</h1>";

    if ($subtitle) {
        echo "<p class=\"text-xl md:text-2xl opacity-90 animate-slide-up\" style=\"animation-delay: 0.1s;\">{$subtitle}</p>";
    }

    if ($additionalContent) {
        echo "<div class=\"mt-6 animate-slide-up\" style=\"animation-delay: 0.2s;\">{$additionalContent}</div>";
    }

    echo '</div>';
    echo '</div>';
}

/**
 * Génère une carte de service standardisée
 */
function renderSchlutterServiceCard($title, $description, $url, $icon, $additionalClasses = '') {
    $cardClasses = getComponentClasses('card') . ' ' . $additionalClasses;

    echo "<div class=\"{$cardClasses} animate-scale-in group\">";
    echo '<div class="text-center">';
    echo "<div class=\"bg-gradient-to-br from-schluter-orange to-schluter-orange-dark text-white p-4 rounded-xl mb-4 inline-block group-hover:scale-110 transition-transform duration-300\">";
    echo "<i class=\"{$icon} text-2xl\"></i>";
    echo '</div>';
    echo "<h3 class=\"text-xl font-semibold text-schluter-gray mb-3\">{$title}</h3>";
    echo "<p class=\"text-schluter-gray-medium mb-6 leading-relaxed\">{$description}</p>";
    echo "<a href=\"{$url}\" class=\"" . getComponentClasses('button_primary') . "\">";
    echo '<i class="fas fa-arrow-right mr-2"></i>';
    echo 'Accéder';
    echo '</a>';
    echo '</div>';
    echo '</div>';
}

/**
 * Génère un tableau standardisé
 */
function renderSchlutterTableStart($headers, $additionalClasses = '') {
    $tableClasses = getComponentClasses('table') . ' ' . $additionalClasses;
    $headerClasses = getComponentClasses('table_header');

    echo "<div class=\"overflow-x-auto\">";
    echo "<table class=\"{$tableClasses}\">";
    echo "<thead class=\"{$headerClasses}\">";
    echo '<tr>';

    foreach ($headers as $header) {
        echo "<th class=\"px-6 py-4 text-left text-sm font-semibold uppercase tracking-wider\">{$header}</th>";
    }

    echo '</tr>';
    echo '</thead>';
    echo '<tbody class="divide-y divide-gray-200">';
}

function renderSchlutterTableEnd() {
    echo '</tbody>';
    echo '</table>';
    echo '</div>';
}

/**
 * Génère un formulaire standardisé
 */
function renderSchlutterFormStart($action = '', $method = 'POST', $additionalClasses = '') {
    $formClasses = getComponentClasses('card') . ' ' . $additionalClasses;

    echo "<form action=\"{$action}\" method=\"{$method}\" class=\"{$formClasses}\">";
}

function renderSchlutterFormEnd() {
    echo '</form>';
}

/**
 * Génère un champ de formulaire standardisé
 */
function renderSchlutterFormField($type, $name, $label, $value = '', $required = false, $additionalAttributes = '') {
    $inputClasses = getComponentClasses('input');
    $requiredAttr = $required ? 'required' : '';

    echo '<div class="mb-6">';
    echo "<label for=\"{$name}\" class=\"block text-sm font-semibold text-schluter-gray mb-2\">";
    echo $label;
    if ($required) echo '<span class="text-schluter-danger ml-1">*</span>';
    echo '</label>';

    if ($type === 'textarea') {
        echo "<textarea id=\"{$name}\" name=\"{$name}\" class=\"{$inputClasses}\" rows=\"4\" {$requiredAttr} {$additionalAttributes}>{$value}</textarea>";
    } elseif ($type === 'select') {
        echo "<select id=\"{$name}\" name=\"{$name}\" class=\"{$inputClasses}\" {$requiredAttr} {$additionalAttributes}>";
        echo $value; // Pour les selects, $value contient les options
        echo '</select>';
    } else {
        echo "<input type=\"{$type}\" id=\"{$name}\" name=\"{$name}\" value=\"{$value}\" class=\"{$inputClasses}\" {$requiredAttr} {$additionalAttributes}>";
    }

    echo '</div>';
}

/**
 * Génère le footer standardisé
 */
function renderSchlutterFooter() {
    echo '<footer class="bg-schluter-gray text-white py-8 mt-16">';
    echo '<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">';
    echo '<div class="text-center">';
    echo '<p class="text-lg">&copy; ' . date('Y') . ' Schluter Systems. Tous droits réservés.</p>';
    echo '<div class="mt-4 flex justify-center space-x-6">';
    echo '<a href="#" class="text-schluter-orange hover:text-schluter-orange-light transition-colors duration-200">Politique de confidentialité</a>';
    echo '<a href="#" class="text-schluter-orange hover:text-schluter-orange-light transition-colors duration-200">Conditions d\'utilisation</a>';
    echo '<a href="#" class="text-schluter-orange hover:text-schluter-orange-light transition-colors duration-200">Support</a>';
    echo '</div>';
    echo '</div>';
    echo '</div>';
    echo '</footer>';

    // Bouton retour en haut
    echo '<button id="backToTop" class="fixed bottom-6 right-6 bg-schluter-orange hover:bg-schluter-orange-dark text-white p-3 rounded-full shadow-schluter-lg transition-all duration-300 opacity-0 invisible hover-scale">';
    echo '<i class="fas fa-arrow-up"></i>';
    echo '</button>';

    // Script pour le bouton retour en haut
    echo '<script>
    window.addEventListener("scroll", function() {
        const backToTop = document.getElementById("backToTop");
        if (window.scrollY > 300) {
            backToTop.classList.remove("opacity-0", "invisible");
            backToTop.classList.add("opacity-100", "visible");
        } else {
            backToTop.classList.add("opacity-0", "invisible");
            backToTop.classList.remove("opacity-100", "visible");
        }
    });

    document.getElementById("backToTop").addEventListener("click", function() {
        window.scrollTo({ top: 0, behavior: "smooth" });
    });
    </script>';

    echo '</body>';
    echo '</html>';
}

/**
 * Génère une notification/alerte standardisée
 */
function renderSchlutterAlert($message, $type = 'info', $dismissible = true) {
    $typeClasses = [
        'success' => 'bg-schluter-success border-schluter-success text-white',
        'warning' => 'bg-schluter-warning border-schluter-warning text-white',
        'danger' => 'bg-schluter-danger border-schluter-danger text-white',
        'info' => 'bg-schluter-info border-schluter-info text-white'
    ];

    $icons = [
        'success' => 'fas fa-check-circle',
        'warning' => 'fas fa-exclamation-triangle',
        'danger' => 'fas fa-exclamation-circle',
        'info' => 'fas fa-info-circle'
    ];

    $classes = $typeClasses[$type] ?? $typeClasses['info'];
    $icon = $icons[$type] ?? $icons['info'];

    echo "<div class=\"{$classes} border-l-4 p-4 rounded-lg shadow-schluter mb-4 animate-slide-up\">";
    echo '<div class="flex items-center justify-between">';
    echo '<div class="flex items-center">';
    echo "<i class=\"{$icon} mr-3\"></i>";
    echo "<span>{$message}</span>";
    echo '</div>';

    if ($dismissible) {
        echo '<button onclick="this.parentElement.parentElement.remove()" class="text-white hover:text-gray-200 transition-colors duration-200">';
        echo '<i class="fas fa-times"></i>';
        echo '</button>';
    }

    echo '</div>';
    echo '</div>';
}
?>
