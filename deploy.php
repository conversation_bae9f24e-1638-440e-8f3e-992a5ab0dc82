<?php
/**
 * Script de déploiement automatisé pour le portail Schluter Systems
 * Gère la migration des assets, la compilation et la mise à jour de la base de données
 */

class SchlutterDeployment {
    private $config;
    private $logFile;
    
    public function __construct() {
        $this->config = [
            'backup_dir' => 'backups',
            'dist_dir' => 'dist',
            'css_source' => 'css/schluter-unified.css',
            'js_source' => 'js/schluter-common.js',
            'css_output' => 'dist/css/schluter.min.css',
            'js_output' => 'dist/js/schluter.min.js'
        ];
        
        $this->logFile = 'deployment.log';
        $this->log("=== Début du déploiement Schluter Systems ===");
    }
    
    /**
     * Exécute le déploiement complet
     */
    public function deploy() {
        try {
            $this->log("Démarrage du processus de déploiement...");
            
            // Étape 1: Vérifications préalables
            $this->checkPrerequisites();
            
            // Étape 2: Sauvegarde
            $this->createBackup();
            
            // Étape 3: Compilation des assets
            $this->compileAssets();
            
            // Étape 4: Migration de la base de données
            $this->migrateDatabase();
            
            // Étape 5: Optimisation
            $this->optimizeAssets();
            
            // Étape 6: Tests de validation
            $this->runValidationTests();
            
            $this->log("✅ Déploiement terminé avec succès!");
            echo "🚀 Déploiement réussi! Consultez {$this->logFile} pour les détails.\n";
            
        } catch (Exception $e) {
            $this->log("❌ Erreur lors du déploiement: " . $e->getMessage());
            echo "❌ Échec du déploiement: " . $e->getMessage() . "\n";
            echo "📋 Consultez {$this->logFile} pour plus de détails.\n";
            exit(1);
        }
    }
    
    /**
     * Vérifications préalables
     */
    private function checkPrerequisites() {
        $this->log("Vérification des prérequis...");
        
        // Vérifier PHP
        if (version_compare(PHP_VERSION, '7.4.0', '<')) {
            throw new Exception("PHP 7.4+ requis. Version actuelle: " . PHP_VERSION);
        }
        
        // Vérifier les extensions PHP
        $requiredExtensions = ['pdo', 'pdo_mysql', 'json', 'mbstring'];
        foreach ($requiredExtensions as $ext) {
            if (!extension_loaded($ext)) {
                throw new Exception("Extension PHP manquante: {$ext}");
            }
        }
        
        // Vérifier Node.js
        $nodeVersion = shell_exec('node --version 2>/dev/null');
        if (!$nodeVersion) {
            $this->log("⚠️  Node.js non détecté. Compilation manuelle requise.");
        } else {
            $this->log("✅ Node.js détecté: " . trim($nodeVersion));
        }
        
        // Créer les dossiers nécessaires
        $this->createDirectories();
        
        $this->log("✅ Prérequis validés");
    }
    
    /**
     * Créer les dossiers nécessaires
     */
    private function createDirectories() {
        $dirs = [
            $this->config['backup_dir'],
            $this->config['dist_dir'],
            $this->config['dist_dir'] . '/css',
            $this->config['dist_dir'] . '/js',
            $this->config['dist_dir'] . '/img'
        ];
        
        foreach ($dirs as $dir) {
            if (!is_dir($dir)) {
                mkdir($dir, 0755, true);
                $this->log("📁 Dossier créé: {$dir}");
            }
        }
    }
    
    /**
     * Créer une sauvegarde
     */
    private function createBackup() {
        $this->log("Création de la sauvegarde...");
        
        $backupName = 'backup_' . date('Y-m-d_H-i-s');
        $backupPath = $this->config['backup_dir'] . '/' . $backupName;
        
        // Sauvegarder les fichiers critiques
        $filesToBackup = [
            'index.php',
            'IT.php',
            'RSE.php',
            'ADV.php',
            'css/',
            'js/',
            'config/'
        ];
        
        mkdir($backupPath, 0755, true);
        
        foreach ($filesToBackup as $file) {
            if (file_exists($file)) {
                if (is_dir($file)) {
                    $this->copyDirectory($file, $backupPath . '/' . $file);
                } else {
                    copy($file, $backupPath . '/' . $file);
                }
                $this->log("💾 Sauvegardé: {$file}");
            }
        }
        
        $this->log("✅ Sauvegarde créée: {$backupPath}");
    }
    
    /**
     * Compiler les assets
     */
    private function compileAssets() {
        $this->log("Compilation des assets...");
        
        // Vérifier si npm est disponible
        $npmAvailable = shell_exec('npm --version 2>/dev/null');
        
        if ($npmAvailable) {
            $this->log("🔧 Compilation avec npm...");
            
            // Installer les dépendances si nécessaire
            if (!is_dir('node_modules')) {
                $this->log("📦 Installation des dépendances npm...");
                $output = shell_exec('npm install 2>&1');
                $this->log("npm install: " . $output);
            }
            
            // Compiler les assets
            $buildOutput = shell_exec('npm run build 2>&1');
            $this->log("npm run build: " . $buildOutput);
            
        } else {
            $this->log("⚠️  npm non disponible, compilation manuelle...");
            $this->manualCompilation();
        }
        
        $this->log("✅ Assets compilés");
    }
    
    /**
     * Compilation manuelle des assets
     */
    private function manualCompilation() {
        // Copier les fichiers CSS et JS
        if (file_exists($this->config['css_source'])) {
            copy($this->config['css_source'], $this->config['css_output']);
            $this->log("📄 CSS copié: " . $this->config['css_output']);
        }
        
        if (file_exists($this->config['js_source'])) {
            copy($this->config['js_source'], $this->config['js_output']);
            $this->log("📄 JS copié: " . $this->config['js_output']);
        }
    }
    
    /**
     * Migration de la base de données
     */
    private function migrateDatabase() {
        $this->log("Migration de la base de données...");
        
        try {
            require_once 'config/database.php';
            $db = getDB();
            
            // Vérifier la connexion
            $db->getConnection();
            $this->log("✅ Connexion à la base de données établie");
            
            // Exécuter les migrations si nécessaire
            $this->runMigrations($db);
            
        } catch (Exception $e) {
            $this->log("❌ Erreur de base de données: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * Exécuter les migrations
     */
    private function runMigrations($db) {
        $migrations = [
            "CREATE TABLE IF NOT EXISTS deployment_log (
                id INT AUTO_INCREMENT PRIMARY KEY,
                version VARCHAR(50),
                deployed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                status ENUM('success', 'failed') DEFAULT 'success'
            )",
            "INSERT INTO deployment_log (version, status) VALUES ('2.0.0', 'success')"
        ];
        
        foreach ($migrations as $migration) {
            try {
                $db->executeQuery($migration);
                $this->log("✅ Migration exécutée: " . substr($migration, 0, 50) . "...");
            } catch (Exception $e) {
                $this->log("⚠️  Migration ignorée (probablement déjà appliquée): " . $e->getMessage());
            }
        }
    }
    
    /**
     * Optimiser les assets
     */
    private function optimizeAssets() {
        $this->log("Optimisation des assets...");
        
        // Minifier le CSS si possible
        if (file_exists($this->config['css_output'])) {
            $css = file_get_contents($this->config['css_output']);
            $minifiedCss = $this->minifyCSS($css);
            file_put_contents($this->config['css_output'], $minifiedCss);
            $this->log("🗜️  CSS minifié");
        }
        
        // Optimiser les images
        $this->optimizeImages();
        
        $this->log("✅ Optimisation terminée");
    }
    
    /**
     * Minifier le CSS
     */
    private function minifyCSS($css) {
        // Supprimer les commentaires
        $css = preg_replace('!/\*[^*]*\*+([^/][^*]*\*+)*/!', '', $css);
        
        // Supprimer les espaces inutiles
        $css = str_replace(["\r\n", "\r", "\n", "\t", '  ', '    ', '    '], '', $css);
        
        return $css;
    }
    
    /**
     * Optimiser les images
     */
    private function optimizeImages() {
        $imageDir = 'img';
        $outputDir = $this->config['dist_dir'] . '/img';
        
        if (is_dir($imageDir)) {
            $this->copyDirectory($imageDir, $outputDir);
            $this->log("🖼️  Images copiées vers {$outputDir}");
        }
    }
    
    /**
     * Tests de validation
     */
    private function runValidationTests() {
        $this->log("Exécution des tests de validation...");
        
        // Test 1: Vérifier que les fichiers compilés existent
        $requiredFiles = [
            $this->config['css_output'],
            $this->config['js_output']
        ];
        
        foreach ($requiredFiles as $file) {
            if (!file_exists($file)) {
                throw new Exception("Fichier manquant après compilation: {$file}");
            }
            $this->log("✅ Fichier validé: {$file}");
        }
        
        // Test 2: Vérifier la syntaxe PHP des fichiers principaux
        $phpFiles = ['index.php', 'IT.php', 'config/database.php'];
        foreach ($phpFiles as $file) {
            if (file_exists($file)) {
                $output = shell_exec("php -l {$file} 2>&1");
                if (strpos($output, 'No syntax errors') === false) {
                    throw new Exception("Erreur de syntaxe PHP dans {$file}: {$output}");
                }
                $this->log("✅ Syntaxe PHP validée: {$file}");
            }
        }
        
        $this->log("✅ Tous les tests de validation réussis");
    }
    
    /**
     * Copier un dossier récursivement
     */
    private function copyDirectory($src, $dst) {
        if (!is_dir($dst)) {
            mkdir($dst, 0755, true);
        }
        
        $files = scandir($src);
        foreach ($files as $file) {
            if ($file != '.' && $file != '..') {
                $srcFile = $src . '/' . $file;
                $dstFile = $dst . '/' . $file;
                
                if (is_dir($srcFile)) {
                    $this->copyDirectory($srcFile, $dstFile);
                } else {
                    copy($srcFile, $dstFile);
                }
            }
        }
    }
    
    /**
     * Logger les messages
     */
    private function log($message) {
        $timestamp = date('Y-m-d H:i:s');
        $logMessage = "[{$timestamp}] {$message}\n";
        file_put_contents($this->logFile, $logMessage, FILE_APPEND | LOCK_EX);
        echo $logMessage;
    }
}

// Exécution du script
if (php_sapi_name() === 'cli') {
    $deployment = new SchlutterDeployment();
    $deployment->deploy();
} else {
    echo "Ce script doit être exécuté en ligne de commande.\n";
    echo "Usage: php deploy.php\n";
}
?>
