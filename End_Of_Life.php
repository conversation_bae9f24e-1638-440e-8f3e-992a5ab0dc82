<?php

function getDbConnection() {
    try {
        $conn = new PDO("mysql:host=localhost;dbname=schluter_db", "root", "");
        $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        return $conn;
    } catch(PDOException $e) {
        die("Erreur de connexion : " . $e->getMessage());
    }
}

function addChangement($nom, $ancien_poste, $nouveau_poste, $date, $lifecycle, $responsable, $type) {
    $conn = getDbConnection();
    $sql = "INSERT INTO changements_poste (nom_utilisateur, ancien_poste, nouveau_poste, date_changement,
            lifecycle, responsable, type) VALUES (?, ?, ?, ?, ?, ?, ?)";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$nom, $ancien_poste, $nouveau_poste, $date, $lifecycle, $responsable, $type]);
}

function deleteChangement($id) {
    $conn = getDbConnection();
    $sql = "DELETE FROM changements_poste WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$id]);
}

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    if (isset($_POST['submit'])) {
        addChangement(
            $_POST['nom'],
            $_POST['ancien_poste'],
            $_POST['nouveau_poste'],
            $_POST['date'],
            $_POST['lifecycle'],
            $_POST['responsable'],
            $_POST['type']
        );
    } elseif (isset($_POST['delete'])) {
        deleteChangement($_POST['id']);
    }
}

// Récupérer les données pour chaque type
$conn = getDbConnection();

$sql_installes = "SELECT * FROM changements_poste WHERE type = 'pc_installes' ORDER BY date_changement DESC";
$stmt_installes = $conn->query($sql_installes);
$pc_installes = $stmt_installes->fetchAll(PDO::FETCH_ASSOC);

$sql_prevus = "SELECT * FROM changements_poste WHERE type = 'pc_prevus' ORDER BY date_changement DESC";
$stmt_prevus = $conn->query($sql_prevus);
$pc_prevus = $stmt_prevus->fetchAll(PDO::FETCH_ASSOC);

$sql_a_prevoir = "SELECT * FROM changements_poste WHERE type = 'pc_a_prevoir' ORDER BY date_changement DESC";
$stmt_a_prevoir = $conn->query($sql_a_prevoir);
$pc_a_prevoir = $stmt_a_prevoir->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Gestion des changements de postes et End of Life - Schluter Systems">
    <title>Changements de Poste - End of Life - Schluter Systems</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        schluter: {
                            orange: '#f57c00',
                            'orange-dark': '#e65100',
                            blue: '#005ea2',
                            'blue-dark': '#004c87',
                            gray: '#333333',
                            'gray-light': '#f4f4f4'
                        }
                    }
                }
            }
        }
    </script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-gray-100">
    <!-- Barre de navigation adaptée pour Tailwind -->
    <header class="fixed top-0 left-0 w-full bg-schluter-gray shadow-lg z-50">
        <div class="flex items-center justify-between px-6 py-4">
            <a href="IT.php" class="flex items-center gap-3 text-white text-xl font-bold hover:text-schluter-orange transition-colors duration-200">
                <img src="img/logo_rgb.svg" alt="Schluter Systems Logo" class="h-10">
                Informatique
            </a>
            <nav class="flex gap-6">
                <a href="accuse_reception.php" class="text-white hover:bg-schluter-orange px-4 py-2 rounded-lg transition-all duration-200 <?= basename($_SERVER['PHP_SELF']) === 'accuse_reception.php' ? 'bg-schluter-orange' : '' ?>">
                    Accusés de Réception
                </a>
                <a href="End_Of_Life.php" class="text-white hover:bg-schluter-orange px-4 py-2 rounded-lg transition-all duration-200 <?= basename($_SERVER['PHP_SELF']) === 'End_Of_Life.php' ? 'bg-schluter-orange' : '' ?>">
                    Changements de Poste
                </a>
                <a href="inventaire.php" class="text-white hover:bg-schluter-orange px-4 py-2 rounded-lg transition-all duration-200 <?= basename($_SERVER['PHP_SELF']) === 'inventaire.php' ? 'bg-schluter-orange' : '' ?>">
                    Inventaire
                </a>
            </nav>
        </div>
    </header>

    <main class="container mx-auto px-4 py-8 mt-20">
        <!-- En-tête avec gradient et statistiques -->
        <div class="bg-gradient-to-r from-schluter-blue to-schluter-orange rounded-lg shadow-lg p-8 mb-8 text-white">
            <div class="flex justify-between items-center mb-6">
                <h1 class="text-4xl font-bold">Gestion des Changements de Poste</h1>
                <div class="flex items-center gap-2">
                    <i class="fas fa-exchange-alt text-3xl"></i>
                    <span class="text-lg opacity-90">End of Life</span>
                </div>
            </div>

            <!-- Statistiques rapides -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="bg-white/20 rounded-lg p-6 backdrop-blur-sm">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-lg opacity-90">PC Installés</p>
                            <h3 class="text-3xl font-bold"><?= count($pc_installes) ?></h3>
                        </div>
                        <i class="fas fa-desktop text-4xl opacity-75"></i>
                    </div>
                    <p class="mt-2 text-sm opacity-75">Postes déjà installés</p>
                </div>

                <div class="bg-white/20 rounded-lg p-6 backdrop-blur-sm">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-lg opacity-90">PC Prévus</p>
                            <h3 class="text-3xl font-bold"><?= count($pc_prevus) ?></h3>
                        </div>
                        <i class="fas fa-calendar-check text-4xl opacity-75"></i>
                    </div>
                    <p class="mt-2 text-sm opacity-75">Installations planifiées</p>
                </div>

                <div class="bg-white/20 rounded-lg p-6 backdrop-blur-sm">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-lg opacity-90">PC à Prévoir</p>
                            <h3 class="text-3xl font-bold"><?= count($pc_a_prevoir) ?></h3>
                        </div>
                        <i class="fas fa-clock text-4xl opacity-75"></i>
                    </div>
                    <p class="mt-2 text-sm opacity-75">En attente de planification</p>
                </div>
            </div>
        </div>

        <!-- Section du formulaire -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-8 form-card">
            <div class="flex items-center mb-6">
                <div class="bg-schluter-orange text-white p-3 rounded-lg mr-4">
                    <i class="fas fa-plus text-xl"></i>
                </div>
                <h2 class="text-2xl font-semibold text-schluter-blue">Ajouter un Changement de Poste</h2>
            </div>

            <form method="POST" action="End_Of_Life.php" class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="type" class="block text-sm font-medium text-gray-700 mb-2">Type de changement</label>
                    <select id="type" name="type" required
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-schluter-orange focus:border-transparent transition-all duration-200">
                        <option value="">Sélectionner un type</option>
                        <option value="pc_installes">PC Installés</option>
                        <option value="pc_prevus">PC Prévus</option>
                        <option value="pc_a_prevoir">PC à Prévoir</option>
                    </select>
                </div>

                <div>
                    <label for="nom" class="block text-sm font-medium text-gray-700 mb-2">Nom utilisateur</label>
                    <input type="text" id="nom" name="nom" required
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-schluter-orange focus:border-transparent transition-all duration-200">
                </div>

                <div>
                    <label for="ancien_poste" class="block text-sm font-medium text-gray-700 mb-2">Ancien poste</label>
                    <input type="text" id="ancien_poste" name="ancien_poste" required
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-schluter-orange focus:border-transparent transition-all duration-200">
                </div>

                <div>
                    <label for="nouveau_poste" class="block text-sm font-medium text-gray-700 mb-2">Nouveau poste</label>
                    <input type="text" id="nouveau_poste" name="nouveau_poste"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-schluter-orange focus:border-transparent transition-all duration-200">
                </div>

                <div>
                    <label for="date" class="block text-sm font-medium text-gray-700 mb-2">Date de changement</label>
                    <input type="date" id="date" name="date" required
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-schluter-orange focus:border-transparent transition-all duration-200">
                </div>

                <div>
                    <label for="lifecycle" class="block text-sm font-medium text-gray-700 mb-2">Lifecycle</label>
                    <input type="text" id="lifecycle" name="lifecycle" required
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-schluter-orange focus:border-transparent transition-all duration-200">
                </div>

                <div class="md:col-span-2">
                    <label for="responsable" class="block text-sm font-medium text-gray-700 mb-2">Fait par</label>
                    <input type="text" id="responsable" name="responsable" required
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-schluter-orange focus:border-transparent transition-all duration-200">
                </div>

                <div class="md:col-span-2">
                    <button type="submit" name="submit"
                            class="w-full bg-schluter-orange hover:bg-schluter-orange-dark text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-200 flex items-center justify-center gap-2">
                        <i class="fas fa-plus"></i>
                        Ajouter le Changement
                    </button>
                </div>
            </form>
        </div>

        <!-- Section des tableaux avec onglets -->
        <div class="bg-white rounded-lg shadow-lg overflow-hidden">
            <!-- En-tête des onglets -->
            <div class="bg-gradient-to-r from-schluter-blue to-schluter-orange p-6">
                <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center">
                        <i class="fas fa-list text-white text-2xl mr-3"></i>
                        <h2 class="text-2xl font-bold text-white">Gestion des Postes</h2>
                    </div>
                </div>

                <!-- Onglets -->
                <div class="flex space-x-1">
                    <button onclick="showTab('installes')" id="tab-installes" class="tab-button active bg-white/20 backdrop-blur-sm text-white px-6 py-3 rounded-lg transition-all duration-200 flex items-center gap-2">
                        <i class="fas fa-desktop"></i>
                        PC Installés (<?= count($pc_installes) ?>)
                    </button>
                    <button onclick="showTab('prevus')" id="tab-prevus" class="tab-button bg-white/10 backdrop-blur-sm text-white px-6 py-3 rounded-lg transition-all duration-200 flex items-center gap-2">
                        <i class="fas fa-calendar-check"></i>
                        PC Prévus (<?= count($pc_prevus) ?>)
                    </button>
                    <button onclick="showTab('a-prevoir')" id="tab-a-prevoir" class="tab-button bg-white/10 backdrop-blur-sm text-white px-6 py-3 rounded-lg transition-all duration-200 flex items-center gap-2">
                        <i class="fas fa-clock"></i>
                        PC à Prévoir (<?= count($pc_a_prevoir) ?>)
                    </button>
                </div>
            </div>

            <!-- Contenu des onglets -->
            <!-- PC Installés -->
            <div id="content-installes" class="tab-content">
                <?php if (empty($pc_installes)): ?>
                    <div class="p-8 text-center">
                        <i class="fas fa-desktop text-gray-400 text-6xl mb-4"></i>
                        <h3 class="text-xl font-semibold text-gray-600 mb-2">Aucun PC installé</h3>
                        <p class="text-gray-500">Les PC installés apparaîtront ici.</p>
                    </div>
                <?php else: ?>
                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        <i class="fas fa-user mr-2"></i>Utilisateur
                                    </th>
                                    <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        <i class="fas fa-arrow-left mr-2"></i>Ancien Poste
                                    </th>
                                    <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        <i class="fas fa-arrow-right mr-2"></i>Nouveau Poste
                                    </th>
                                    <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        <i class="fas fa-calendar mr-2"></i>Date
                                    </th>
                                    <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        <i class="fas fa-recycle mr-2"></i>Lifecycle
                                    </th>
                                    <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        <i class="fas fa-user-check mr-2"></i>Responsable
                                    </th>
                                    <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        <i class="fas fa-cog mr-2"></i>Actions
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <?php foreach ($pc_installes as $index => $changement): ?>
                                    <tr class="hover:bg-gray-50 transition-colors duration-200 table-row">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                            <div class="flex items-center">
                                                <div class="bg-green-100 text-green-800 p-2 rounded-full mr-3">
                                                    <i class="fas fa-user text-sm"></i>
                                                </div>
                                                <?= htmlspecialchars($changement['nom_utilisateur']) ?>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                                            <span class="bg-red-100 text-red-800 px-2 py-1 rounded-lg text-xs">
                                                <?= htmlspecialchars($changement['ancien_poste']) ?>
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                                            <span class="bg-green-100 text-green-800 px-2 py-1 rounded-lg text-xs">
                                                <?= htmlspecialchars($changement['nouveau_poste']) ?>
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            <div class="flex items-center">
                                                <i class="fas fa-calendar text-schluter-blue mr-2"></i>
                                                <?= date('d/m/Y', strtotime($changement['date_changement'])) ?>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                                            <span class="bg-schluter-orange text-white px-2 py-1 rounded-lg text-xs">
                                                <?= htmlspecialchars($changement['lifecycle']) ?>
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                                            <?= htmlspecialchars($changement['responsable']) ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm">
                                            <form method="POST" action="End_Of_Life.php" style="display:inline;">
                                                <input type="hidden" name="id" value="<?= $changement['id'] ?>">
                                                <button type="submit" name="delete"
                                                        class="bg-red-500 hover:bg-red-600 text-white px-3 py-1 rounded-lg transition-colors duration-200 flex items-center gap-1"
                                                        onclick="return confirm('Êtes-vous sûr de vouloir supprimer ce changement ?')">
                                                    <i class="fas fa-trash text-xs"></i>
                                                    Supprimer
                                                </button>
                                            </form>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>

            <!-- PC Prévus -->
            <div id="content-prevus" class="tab-content hidden">
                <?php if (empty($pc_prevus)): ?>
                    <div class="p-8 text-center">
                        <i class="fas fa-calendar-check text-gray-400 text-6xl mb-4"></i>
                        <h3 class="text-xl font-semibold text-gray-600 mb-2">Aucun PC prévu</h3>
                        <p class="text-gray-500">Les PC prévus pour installation apparaîtront ici.</p>
                    </div>
                <?php else: ?>
                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        <i class="fas fa-user mr-2"></i>Utilisateur
                                    </th>
                                    <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        <i class="fas fa-arrow-left mr-2"></i>Ancien Poste
                                    </th>
                                    <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        <i class="fas fa-arrow-right mr-2"></i>Nouveau Poste
                                    </th>
                                    <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        <i class="fas fa-calendar mr-2"></i>Date Prévue
                                    </th>
                                    <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        <i class="fas fa-recycle mr-2"></i>Lifecycle
                                    </th>
                                    <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        <i class="fas fa-user-check mr-2"></i>Responsable
                                    </th>
                                    <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        <i class="fas fa-cog mr-2"></i>Actions
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <?php foreach ($pc_prevus as $index => $changement): ?>
                                    <tr class="hover:bg-gray-50 transition-colors duration-200 table-row">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                            <div class="flex items-center">
                                                <div class="bg-blue-100 text-blue-800 p-2 rounded-full mr-3">
                                                    <i class="fas fa-user text-sm"></i>
                                                </div>
                                                <?= htmlspecialchars($changement['nom_utilisateur']) ?>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                                            <span class="bg-red-100 text-red-800 px-2 py-1 rounded-lg text-xs">
                                                <?= htmlspecialchars($changement['ancien_poste']) ?>
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                                            <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded-lg text-xs">
                                                <?= htmlspecialchars($changement['nouveau_poste']) ?>
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            <div class="flex items-center">
                                                <i class="fas fa-calendar text-schluter-blue mr-2"></i>
                                                <?= date('d/m/Y', strtotime($changement['date_changement'])) ?>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                                            <span class="bg-schluter-orange text-white px-2 py-1 rounded-lg text-xs">
                                                <?= htmlspecialchars($changement['lifecycle']) ?>
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                                            <?= htmlspecialchars($changement['responsable']) ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm">
                                            <form method="POST" action="End_Of_Life.php" style="display:inline;">
                                                <input type="hidden" name="id" value="<?= $changement['id'] ?>">
                                                <button type="submit" name="delete"
                                                        class="bg-red-500 hover:bg-red-600 text-white px-3 py-1 rounded-lg transition-colors duration-200 flex items-center gap-1"
                                                        onclick="return confirm('Êtes-vous sûr de vouloir supprimer ce changement ?')">
                                                    <i class="fas fa-trash text-xs"></i>
                                                    Supprimer
                                                </button>
                                            </form>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>

            <!-- PC à Prévoir -->
            <div id="content-a-prevoir" class="tab-content hidden">
                <?php if (empty($pc_a_prevoir)): ?>
                    <div class="p-8 text-center">
                        <i class="fas fa-clock text-gray-400 text-6xl mb-4"></i>
                        <h3 class="text-xl font-semibold text-gray-600 mb-2">Aucun PC à prévoir</h3>
                        <p class="text-gray-500">Les PC à prévoir apparaîtront ici.</p>
                    </div>
                <?php else: ?>
                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        <i class="fas fa-user mr-2"></i>Utilisateur
                                    </th>
                                    <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        <i class="fas fa-arrow-left mr-2"></i>Ancien Poste
                                    </th>
                                    <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        <i class="fas fa-arrow-right mr-2"></i>Nouveau Poste
                                    </th>
                                    <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        <i class="fas fa-calendar mr-2"></i>Date Estimée
                                    </th>
                                    <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        <i class="fas fa-recycle mr-2"></i>Lifecycle
                                    </th>
                                    <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        <i class="fas fa-user-check mr-2"></i>Responsable
                                    </th>
                                    <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        <i class="fas fa-cog mr-2"></i>Actions
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <?php foreach ($pc_a_prevoir as $index => $changement): ?>
                                    <tr class="hover:bg-gray-50 transition-colors duration-200 table-row">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                            <div class="flex items-center">
                                                <div class="bg-yellow-100 text-yellow-800 p-2 rounded-full mr-3">
                                                    <i class="fas fa-user text-sm"></i>
                                                </div>
                                                <?= htmlspecialchars($changement['nom_utilisateur']) ?>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                                            <span class="bg-red-100 text-red-800 px-2 py-1 rounded-lg text-xs">
                                                <?= htmlspecialchars($changement['ancien_poste']) ?>
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                                            <span class="bg-yellow-100 text-yellow-800 px-2 py-1 rounded-lg text-xs">
                                                <?= htmlspecialchars($changement['nouveau_poste']) ?>
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            <div class="flex items-center">
                                                <i class="fas fa-calendar text-schluter-blue mr-2"></i>
                                                <?= date('d/m/Y', strtotime($changement['date_changement'])) ?>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                                            <span class="bg-schluter-orange text-white px-2 py-1 rounded-lg text-xs">
                                                <?= htmlspecialchars($changement['lifecycle']) ?>
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                                            <?= htmlspecialchars($changement['responsable']) ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm">
                                            <form method="POST" action="End_Of_Life.php" style="display:inline;">
                                                <input type="hidden" name="id" value="<?= $changement['id'] ?>">
                                                <button type="submit" name="delete"
                                                        class="bg-red-500 hover:bg-red-600 text-white px-3 py-1 rounded-lg transition-colors duration-200 flex items-center gap-1"
                                                        onclick="return confirm('Êtes-vous sûr de vouloir supprimer ce changement ?')">
                                                    <i class="fas fa-trash text-xs"></i>
                                                    Supprimer
                                                </button>
                                            </form>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </main>

    <!-- Scripts d'animation et fonctionnalités -->
    <script>
        // Gestion des onglets
        function showTab(tabName) {
            // Masquer tous les contenus
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.add('hidden');
            });

            // Désactiver tous les boutons d'onglets
            document.querySelectorAll('.tab-button').forEach(button => {
                button.classList.remove('active', 'bg-white/20');
                button.classList.add('bg-white/10');
            });

            // Afficher le contenu sélectionné
            document.getElementById('content-' + tabName).classList.remove('hidden');

            // Activer le bouton sélectionné
            const activeButton = document.getElementById('tab-' + tabName);
            activeButton.classList.add('active', 'bg-white/20');
            activeButton.classList.remove('bg-white/10');
        }

        // Animation des éléments au chargement
        document.addEventListener('DOMContentLoaded', () => {
            // Animation du formulaire
            gsap.from('.form-card', {
                duration: 0.6,
                y: 50,
                opacity: 0,
                ease: 'power2.out'
            });

            // Animation des lignes du tableau
            gsap.from('.table-row', {
                duration: 0.4,
                x: -20,
                opacity: 0,
                stagger: 0.05,
                ease: 'power2.out',
                delay: 0.8
            });

            // Animation de l'en-tête
            gsap.from('.bg-gradient-to-r', {
                duration: 0.8,
                scale: 0.95,
                opacity: 0,
                ease: 'power2.out'
            });

            // Animation des onglets
            gsap.from('.tab-button', {
                duration: 0.5,
                y: -20,
                opacity: 0,
                stagger: 0.1,
                ease: 'power2.out',
                delay: 0.3
            });
        });

        // Effets sur les champs de formulaire
        document.querySelectorAll('input, select').forEach(input => {
            input.addEventListener('focus', function() {
                this.style.transform = 'scale(1.02)';
            });

            input.addEventListener('blur', function() {
                this.style.transform = 'scale(1)';
            });
        });

        // Auto-définir la date d'aujourd'hui par défaut
        document.getElementById('date').valueAsDate = new Date();
    </script>

    <?php include 'bottom_bar.php'; ?>
</body>
</html>