<?php
/**
 * Script d'initialisation de la base de données Schluter Systems
 * Ce script crée la base de données et toutes les tables nécessaires
 */

// Configuration de la base de données
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'schluter_db_dev'; // Utiliser la base de développement

try {
    echo "<h2>Initialisation de la base de données Schluter Systems</h2>";

    // Connexion à MySQL sans sélectionner la base de données
    $pdo = new PDO("mysql:host=$host", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);

    // Créer la base de données si elle n'existe pas
    $pdo->exec("CREATE DATABASE IF NOT EXISTS `$database` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "✅ Base de données '$database' créée avec succès<br>";

    // Sélectionner la base de données
    $pdo->exec("USE `$database`");

    // Lire et exécuter le fichier SQL principal
    $sqlFile = 'database/schluter_db.sql';
    if (file_exists($sqlFile)) {
        $sql = file_get_contents($sqlFile);

        // Diviser le SQL en requêtes individuelles
        $queries = array_filter(array_map('trim', explode(';', $sql)));

        foreach ($queries as $query) {
            if (!empty($query)) {
                try {
                    $pdo->exec($query);
                } catch (PDOException $e) {
                    // Ignorer les erreurs de duplication (tables déjà existantes)
                    if (strpos($e->getMessage(), 'already exists') === false &&
                        strpos($e->getMessage(), 'Duplicate entry') === false) {
                        echo "⚠️ Avertissement: " . $e->getMessage() . "<br>";
                    }
                }
            }
        }
        echo "✅ Structure de la base de données importée avec succès<br>";
    } else {
        echo "❌ Fichier SQL non trouvé: $sqlFile<br>";
    }

    // Vérifier que les tables importantes existent
    $requiredTables = ['users', 'services', 'evaluations', 'inventaire', 'tickets'];
    foreach ($requiredTables as $table) {
        $result = $pdo->query("SHOW TABLES LIKE '$table'")->fetch();
        if ($result) {
            echo "✅ Table '$table' existe<br>";
        } else {
            echo "❌ Table '$table' manquante<br>";
        }
    }

    // Vérifier le contenu de la table services
    $serviceCount = $pdo->query("SELECT COUNT(*) as count FROM services")->fetch()['count'];
    echo "ℹ️ Nombre de services dans la base: $serviceCount<br>";

    echo "<br><strong>✅ Initialisation terminée avec succès!</strong><br>";
    echo "<a href='index.php'>Retourner à l'accueil</a>";

} catch(PDOException $e) {
    echo "❌ Erreur : " . $e->getMessage() . "<br>";
    echo "Détails: " . $e->getTraceAsString();
}
?>
