<?php
/**
 * Tests système pour valider les améliorations du portail Schluter Systems
 * Vérifie le bon fonctionnement des nouveaux composants et configurations
 */

require_once __DIR__ . '/../config/environment.php';
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../config/schluter-config.php';
require_once __DIR__ . '/../templates/components.php';

class SchlutterSystemTest {
    private $results = [];
    private $totalTests = 0;
    private $passedTests = 0;

    public function __construct() {
        echo "🧪 Tests Système Schluter Systems\n";
        echo "================================\n\n";
    }

    /**
     * Exécuter tous les tests
     */
    public function runAllTests() {
        $this->testEnvironmentConfiguration();
        $this->testDatabaseConnection();
        $this->testSchlutterConfig();
        $this->testComponentsRendering();
        $this->testAssetFiles();
        $this->testResponsiveDesign();
        $this->testAccessibility();

        $this->displayResults();
    }

    /**
     * Test de la configuration d'environnement
     */
    private function testEnvironmentConfiguration() {
        echo "📋 Test de la configuration d'environnement...\n";

        // Test 1: Détection de l'environnement
        $this->assert(
            defined('SCHLUTER_ENV'),
            "L'environnement doit être détecté et défini"
        );

        // Test 2: Configuration chargée
        $this->assert(
            defined('SCHLUTER_CONFIG'),
            "La configuration doit être chargée"
        );

        // Test 3: Configuration valide
        $config = SCHLUTER_CONFIG;
        $this->assert(
            isset($config['database']) && isset($config['assets']),
            "La configuration doit contenir les sections database et assets"
        );

        // Test 4: Gestionnaires d'erreurs
        $this->assert(
            function_exists('schlutterErrorHandler'),
            "Le gestionnaire d'erreurs personnalisé doit être défini"
        );

        echo "✅ Configuration d'environnement validée\n\n";
    }

    /**
     * Test de la connexion à la base de données
     */
    private function testDatabaseConnection() {
        echo "🗄️  Test de la connexion à la base de données...\n";

        try {
            // Test 1: Instance Singleton
            $db1 = getDB();
            $db2 = getDB();
            $this->assert(
                $db1 === $db2,
                "La classe Database doit utiliser le pattern Singleton"
            );

            // Test 2: Connexion PDO
            $connection = $db1->getConnection();
            $this->assert(
                $connection instanceof PDO,
                "La connexion doit retourner une instance PDO"
            );

            // Test 3: Requête simple
            $result = $db1->fetchColumn("SELECT 1");
            $this->assert(
                $result == 1,
                "Une requête simple doit fonctionner"
            );

            // Test 4: Méthodes utilitaires
            $this->assert(
                method_exists($db1, 'fetchAll') && method_exists($db1, 'insert'),
                "Les méthodes utilitaires doivent être disponibles"
            );

            echo "✅ Connexion à la base de données validée\n\n";

        } catch (Exception $e) {
            $this->assert(false, "Erreur de connexion DB: " . $e->getMessage());
            echo "❌ Échec de la connexion à la base de données\n\n";
        }
    }

    /**
     * Test de la configuration Schluter
     */
    private function testSchlutterConfig() {
        echo "🎨 Test de la configuration Schluter...\n";

        // Test 1: Constantes de couleurs
        $this->assert(
            defined('SCHLUTER_COLORS'),
            "Les couleurs Schluter doivent être définies"
        );

        // Test 2: Couleurs principales
        $colors = SCHLUTER_COLORS;
        $this->assert(
            isset($colors['orange']) && $colors['orange'] === '#f57c00',
            "La couleur orange Schluter doit être correcte"
        );

        $this->assert(
            isset($colors['blue']) && $colors['blue'] === '#005ea2',
            "La couleur bleue Schluter doit être correcte"
        );

        // Test 3: Configuration Tailwind
        $tailwindConfig = getSchlutterTailwindConfig();
        $this->assert(
            !empty($tailwindConfig) && strpos($tailwindConfig, 'schluter') !== false,
            "La configuration Tailwind doit contenir les couleurs Schluter"
        );

        // Test 4: Classes de composants
        $this->assert(
            defined('SCHLUTER_COMPONENT_CLASSES'),
            "Les classes de composants doivent être définies"
        );

        echo "✅ Configuration Schluter validée\n\n";
    }

    /**
     * Test du rendu des composants
     */
    private function testComponentsRendering() {
        echo "🧩 Test du rendu des composants...\n";

        // Test 1: Fonction de génération du header
        $this->assert(
            function_exists('renderSchlutterHeader'),
            "La fonction renderSchlutterHeader doit exister"
        );

        // Test 2: Fonction de navigation
        $this->assert(
            function_exists('renderSchlutterNavigation'),
            "La fonction renderSchlutterNavigation doit exister"
        );

        // Test 3: Fonction de carte de service
        $this->assert(
            function_exists('renderSchlutterServiceCard'),
            "La fonction renderSchlutterServiceCard doit exister"
        );

        // Test 4: Génération de contenu (capture de sortie)
        ob_start();
        renderSchlutterAlert('Test message', 'success');
        $output = ob_get_clean();

        $this->assert(
            !empty($output) && strpos($output, 'Test message') !== false,
            "Les composants doivent générer du contenu HTML"
        );

        echo "✅ Rendu des composants validé\n\n";
    }

    /**
     * Test des fichiers d'assets
     */
    private function testAssetFiles() {
        echo "📁 Test des fichiers d'assets...\n";

        // Test 1: CSS unifié
        $this->assert(
            file_exists(__DIR__ . '/../css/schluter-unified.css'),
            "Le fichier CSS unifié doit exister"
        );

        // Test 2: JavaScript commun
        $this->assert(
            file_exists(__DIR__ . '/../js/schluter-common.js'),
            "Le fichier JavaScript commun doit exister"
        );

        // Test 3: Configuration Tailwind
        $this->assert(
            file_exists(__DIR__ . '/../config/tailwind.config.js'),
            "La configuration Tailwind doit exister"
        );

        // Test 4: Package.json
        $this->assert(
            file_exists(__DIR__ . '/../package.json'),
            "Le fichier package.json doit exister"
        );

        // Test 5: Contenu CSS valide
        $cssContent = file_get_contents(__DIR__ . '/../css/schluter-unified.css');
        $this->assert(
            strpos($cssContent, '--schluter-orange') !== false,
            "Le CSS doit contenir les variables Schluter"
        );

        echo "✅ Fichiers d'assets validés\n\n";
    }

    /**
     * Test du design responsive
     */
    private function testResponsiveDesign() {
        echo "📱 Test du design responsive...\n";

        // Test 1: Media queries dans le CSS
        $cssContent = file_get_contents(__DIR__ . '/../css/schluter-unified.css');
        $this->assert(
            strpos($cssContent, '@media') !== false,
            "Le CSS doit contenir des media queries"
        );

        // Test 2: Classes responsive
        $this->assert(
            strpos($cssContent, 'max-width: 768px') !== false,
            "Le CSS doit contenir des breakpoints mobiles"
        );

        // Test 3: Grilles adaptatives
        $this->assert(
            strpos($cssContent, 'grid-cols-1') !== false || strpos($cssContent, 'flex-direction: column') !== false,
            "Le CSS doit contenir des layouts adaptatifs"
        );

        echo "✅ Design responsive validé\n\n";
    }

    /**
     * Test de l'accessibilité
     */
    private function testAccessibility() {
        echo "♿ Test de l'accessibilité...\n";

        // Test 1: Focus states
        $cssContent = file_get_contents(__DIR__ . '/../css/schluter-unified.css');
        $this->assert(
            strpos($cssContent, ':focus') !== false,
            "Le CSS doit contenir des styles de focus"
        );

        // Test 2: Contraste des couleurs (vérification basique)
        $this->assert(
            strpos($cssContent, 'outline') !== false,
            "Le CSS doit contenir des styles d'outline pour l'accessibilité"
        );

        // Test 3: JavaScript accessible
        $jsContent = file_get_contents(__DIR__ . '/../js/schluter-common.js');
        $this->assert(
            strpos($jsContent, 'addEventListener') !== false,
            "Le JavaScript doit utiliser des event listeners appropriés"
        );

        echo "✅ Accessibilité validée\n\n";
    }

    /**
     * Fonction d'assertion
     */
    private function assert($condition, $message) {
        $this->totalTests++;

        if ($condition) {
            $this->passedTests++;
            $this->results[] = "✅ " . $message;
        } else {
            $this->results[] = "❌ " . $message;
        }
    }

    /**
     * Afficher les résultats
     */
    private function displayResults() {
        echo "📊 Résultats des Tests\n";
        echo "=====================\n\n";

        foreach ($this->results as $result) {
            echo $result . "\n";
        }

        echo "\n";
        echo "Total des tests: {$this->totalTests}\n";
        echo "Tests réussis: {$this->passedTests}\n";
        echo "Tests échoués: " . ($this->totalTests - $this->passedTests) . "\n";

        $percentage = round(($this->passedTests / $this->totalTests) * 100, 2);
        echo "Taux de réussite: {$percentage}%\n\n";

        if ($percentage >= 90) {
            echo "🎉 Excellent! Le système est prêt pour la production.\n";
        } elseif ($percentage >= 75) {
            echo "👍 Bon! Quelques améliorations mineures peuvent être apportées.\n";
        } else {
            echo "⚠️  Attention! Des problèmes importants doivent être résolus.\n";
        }
    }
}

// Exécution des tests si le script est appelé directement
if (php_sapi_name() === 'cli') {
    $tester = new SchlutterSystemTest();
    $tester->runAllTests();
} else {
    echo "Ce script doit être exécuté en ligne de commande.\n";
    echo "Usage: php tests/system-test.php\n";
}
?>
