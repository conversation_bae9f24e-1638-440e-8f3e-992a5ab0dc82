<?php
/**
 * Configuration des environnements pour le portail Schluter Systems
 * Gère les différences entre développement, test et production
 */

// Détection automatique de l'environnement
function detectEnvironment() {
    // Vérifier les variables d'environnement
    if (isset($_ENV['SCHLUTER_ENV'])) {
        return $_ENV['SCHLUTER_ENV'];
    }
    
    // Vérifier le nom d'hôte
    $hostname = $_SERVER['HTTP_HOST'] ?? $_SERVER['SERVER_NAME'] ?? 'localhost';
    
    if (strpos($hostname, 'localhost') !== false || strpos($hostname, '127.0.0.1') !== false) {
        return 'development';
    } elseif (strpos($hostname, 'test') !== false || strpos($hostname, 'staging') !== false) {
        return 'testing';
    } else {
        return 'production';
    }
}

// Configuration par environnement
function getEnvironmentConfig($env = null) {
    if ($env === null) {
        $env = detectEnvironment();
    }
    
    $configs = [
        'development' => [
            'debug' => true,
            'error_reporting' => E_ALL,
            'display_errors' => true,
            'log_errors' => true,
            'assets' => [
                'use_cdn' => false,
                'minified' => false,
                'cache_bust' => true
            ],
            'database' => [
                'host' => 'localhost',
                'dbname' => 'schluter_db_dev',
                'username' => 'root',
                'password' => '',
                'charset' => 'utf8mb4',
                'options' => [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::ATTR_EMULATE_PREPARES => false
                ]
            ],
            'cache' => [
                'enabled' => false,
                'driver' => 'file',
                'ttl' => 300
            ],
            'mail' => [
                'driver' => 'log',
                'from' => '<EMAIL>'
            ],
            'security' => [
                'csrf_protection' => true,
                'session_secure' => false,
                'session_httponly' => true,
                'password_min_length' => 6
            ]
        ],
        
        'testing' => [
            'debug' => true,
            'error_reporting' => E_ALL & ~E_NOTICE,
            'display_errors' => false,
            'log_errors' => true,
            'assets' => [
                'use_cdn' => false,
                'minified' => true,
                'cache_bust' => true
            ],
            'database' => [
                'host' => 'localhost',
                'dbname' => 'schluter_db_test',
                'username' => 'schluter_test',
                'password' => 'test_password_2024',
                'charset' => 'utf8mb4',
                'options' => [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::ATTR_EMULATE_PREPARES => false
                ]
            ],
            'cache' => [
                'enabled' => true,
                'driver' => 'file',
                'ttl' => 600
            ],
            'mail' => [
                'driver' => 'smtp',
                'host' => 'smtp.test.local',
                'from' => '<EMAIL>'
            ],
            'security' => [
                'csrf_protection' => true,
                'session_secure' => true,
                'session_httponly' => true,
                'password_min_length' => 8
            ]
        ],
        
        'production' => [
            'debug' => false,
            'error_reporting' => E_ERROR | E_WARNING | E_PARSE,
            'display_errors' => false,
            'log_errors' => true,
            'assets' => [
                'use_cdn' => true,
                'minified' => true,
                'cache_bust' => false
            ],
            'database' => [
                'host' => 'localhost',
                'dbname' => 'schluter_db',
                'username' => 'schluter_prod',
                'password' => 'secure_password_2024',
                'charset' => 'utf8mb4',
                'options' => [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::ATTR_EMULATE_PREPARES => false,
                    PDO::ATTR_PERSISTENT => true
                ]
            ],
            'cache' => [
                'enabled' => true,
                'driver' => 'redis',
                'ttl' => 3600,
                'redis' => [
                    'host' => '127.0.0.1',
                    'port' => 6379,
                    'database' => 0
                ]
            ],
            'mail' => [
                'driver' => 'smtp',
                'host' => 'smtp.schluter-systems.com',
                'port' => 587,
                'encryption' => 'tls',
                'from' => '<EMAIL>'
            ],
            'security' => [
                'csrf_protection' => true,
                'session_secure' => true,
                'session_httponly' => true,
                'password_min_length' => 12,
                'rate_limiting' => true
            ]
        ]
    ];
    
    return $configs[$env] ?? $configs['production'];
}

// Initialiser l'environnement
function initializeEnvironment() {
    $env = detectEnvironment();
    $config = getEnvironmentConfig($env);
    
    // Configuration PHP
    error_reporting($config['error_reporting']);
    ini_set('display_errors', $config['display_errors'] ? '1' : '0');
    ini_set('log_errors', $config['log_errors'] ? '1' : '0');
    
    // Configuration des sessions
    if ($config['security']['session_secure']) {
        ini_set('session.cookie_secure', '1');
    }
    if ($config['security']['session_httponly']) {
        ini_set('session.cookie_httponly', '1');
    }
    
    // Définir les constantes globales
    define('SCHLUTER_ENV', $env);
    define('SCHLUTER_DEBUG', $config['debug']);
    define('SCHLUTER_CONFIG', $config);
    
    return $config;
}

// Fonctions utilitaires
function isProduction() {
    return (defined('SCHLUTER_ENV') && SCHLUTER_ENV === 'production');
}

function isDevelopment() {
    return (defined('SCHLUTER_ENV') && SCHLUTER_ENV === 'development');
}

function isTesting() {
    return (defined('SCHLUTER_ENV') && SCHLUTER_ENV === 'testing');
}

function getAssetUrl($asset) {
    $config = SCHLUTER_CONFIG;
    $baseUrl = '';
    
    if ($config['assets']['use_cdn']) {
        $baseUrl = 'https://cdn.schluter-systems.com/portal/';
    }
    
    $version = '';
    if ($config['assets']['cache_bust']) {
        $version = '?v=' . filemtime($asset);
    }
    
    $minSuffix = '';
    if ($config['assets']['minified'] && strpos($asset, '.min.') === false) {
        $pathInfo = pathinfo($asset);
        $minSuffix = $pathInfo['dirname'] . '/' . $pathInfo['filename'] . '.min.' . $pathInfo['extension'];
        if (file_exists($minSuffix)) {
            $asset = $minSuffix;
        }
    }
    
    return $baseUrl . $asset . $version;
}

function logError($message, $context = []) {
    $config = SCHLUTER_CONFIG;
    
    if ($config['log_errors']) {
        $logMessage = date('Y-m-d H:i:s') . ' [ERROR] ' . $message;
        if (!empty($context)) {
            $logMessage .= ' Context: ' . json_encode($context);
        }
        error_log($logMessage);
    }
}

function logInfo($message, $context = []) {
    if (isDevelopment() || isTesting()) {
        $logMessage = date('Y-m-d H:i:s') . ' [INFO] ' . $message;
        if (!empty($context)) {
            $logMessage .= ' Context: ' . json_encode($context);
        }
        error_log($logMessage);
    }
}

// Gestionnaire d'erreurs personnalisé
function schlutterErrorHandler($errno, $errstr, $errfile, $errline) {
    if (!(error_reporting() & $errno)) {
        return false;
    }
    
    $errorTypes = [
        E_ERROR => 'ERROR',
        E_WARNING => 'WARNING',
        E_PARSE => 'PARSE',
        E_NOTICE => 'NOTICE',
        E_CORE_ERROR => 'CORE_ERROR',
        E_CORE_WARNING => 'CORE_WARNING',
        E_COMPILE_ERROR => 'COMPILE_ERROR',
        E_COMPILE_WARNING => 'COMPILE_WARNING',
        E_USER_ERROR => 'USER_ERROR',
        E_USER_WARNING => 'USER_WARNING',
        E_USER_NOTICE => 'USER_NOTICE',
        E_STRICT => 'STRICT',
        E_RECOVERABLE_ERROR => 'RECOVERABLE_ERROR',
        E_DEPRECATED => 'DEPRECATED',
        E_USER_DEPRECATED => 'USER_DEPRECATED'
    ];
    
    $errorType = $errorTypes[$errno] ?? 'UNKNOWN';
    $message = "[$errorType] $errstr in $errfile on line $errline";
    
    logError($message);
    
    if (isDevelopment()) {
        echo "<div style='background: #ffebee; border: 1px solid #f44336; padding: 10px; margin: 10px; border-radius: 4px;'>";
        echo "<strong>$errorType:</strong> $errstr<br>";
        echo "<strong>File:</strong> $errfile<br>";
        echo "<strong>Line:</strong> $errline";
        echo "</div>";
    }
    
    return true;
}

// Gestionnaire d'exceptions personnalisé
function schlutterExceptionHandler($exception) {
    $message = "Uncaught exception: " . $exception->getMessage() . " in " . $exception->getFile() . " on line " . $exception->getLine();
    logError($message, ['trace' => $exception->getTraceAsString()]);
    
    if (isDevelopment()) {
        echo "<div style='background: #ffebee; border: 1px solid #f44336; padding: 15px; margin: 10px; border-radius: 4px;'>";
        echo "<h3>Exception non gérée</h3>";
        echo "<strong>Message:</strong> " . htmlspecialchars($exception->getMessage()) . "<br>";
        echo "<strong>Fichier:</strong> " . htmlspecialchars($exception->getFile()) . "<br>";
        echo "<strong>Ligne:</strong> " . $exception->getLine() . "<br>";
        echo "<strong>Trace:</strong><pre>" . htmlspecialchars($exception->getTraceAsString()) . "</pre>";
        echo "</div>";
    } else {
        echo "<div style='text-align: center; padding: 50px;'>";
        echo "<h2>Une erreur est survenue</h2>";
        echo "<p>Nous nous excusons pour la gêne occasionnée. L'équipe technique a été notifiée.</p>";
        echo "</div>";
    }
}

// Initialisation automatique
$config = initializeEnvironment();

// Enregistrer les gestionnaires d'erreurs
set_error_handler('schlutterErrorHandler');
set_exception_handler('schlutterExceptionHandler');

// Retourner la configuration pour utilisation
return $config;
?>
