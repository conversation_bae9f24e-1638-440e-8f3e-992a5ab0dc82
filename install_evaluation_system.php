<?php
/**
 * Script d'installation pour le système d'évaluation des prestataires
 * Schluter Systems
 *
 * Ce script crée automatiquement toutes les tables nécessaires
 * et insère des données de test.
 */

require_once __DIR__ . '/config/database.php';

function installEvaluationSystem($db) {
    $success = true;
    $messages = [];

    try {
        // 1. Créer la table des évaluations
        $db->exec("CREATE TABLE IF NOT EXISTS `evaluations` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `prestataire` varchar(255) NOT NULL,
            `service` varchar(255) NOT NULL,
            `qualite` int(1) NOT NULL CHECK (`qualite` >= 1 AND `qualite` <= 5),
            `delai` int(1) NOT NULL CHECK (`delai` >= 1 AND `delai` <= 5),
            `communication` int(1) NOT NULL CHECK (`communication` >= 1 AND `communication` <= 5),
            `commentaires` text,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            INDEX `idx_prestataire` (`prestataire`),
            INDEX `idx_service` (`service`),
            INDEX `idx_created_at` (`created_at`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
        $messages[] = "✅ Table 'evaluations' créée avec succès";

        // 2. Créer la table des prestataires
        $db->exec("CREATE TABLE IF NOT EXISTS `prestataires` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `nom` varchar(255) NOT NULL UNIQUE,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `unique_nom` (`nom`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
        $messages[] = "✅ Table 'prestataires' créée avec succès";

        // 3. Créer la table des services
        $db->exec("CREATE TABLE IF NOT EXISTS `services` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `nom` varchar(255) NOT NULL UNIQUE,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `unique_nom` (`nom`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
        $messages[] = "✅ Table 'services' créée avec succès";

        // 4. Créer la table des notes des prestataires
        $db->exec("CREATE TABLE IF NOT EXISTS `provider_ratings` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `prestataire` varchar(255) NOT NULL,
            `note` int(1) NOT NULL CHECK (`note` >= 1 AND `note` <= 5),
            `commentaire` text,
            `user_ip` varchar(45) NOT NULL,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `unique_user_provider` (`prestataire`, `user_ip`),
            INDEX `idx_prestataire` (`prestataire`),
            INDEX `idx_note` (`note`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
        $messages[] = "✅ Table 'provider_ratings' créée avec succès";

        // 5. Insérer des prestataires par défaut
        $prestataires = [
            'Entreprise A', 'Entreprise B', 'Entreprise C',
            'Fournisseur Alpha', 'Fournisseur Beta',
            'TechCorp Solutions', 'Digital Services Pro',
            'Maintenance Plus', 'Support Expert'
        ];

        $stmt = $db->prepare("INSERT IGNORE INTO `prestataires` (`nom`) VALUES (?)");
        foreach ($prestataires as $prestataire) {
            $stmt->execute([$prestataire]);
        }
        $messages[] = "✅ Prestataires par défaut ajoutés";

        // 6. Insérer des services par défaut
        $services = [
            'Maintenance informatique', 'Développement web', 'Support technique',
            'Formation', 'Consultation', 'Installation matériel',
            'Réparation', 'Nettoyage', 'Sécurité', 'Audit',
            'Sauvegarde de données', 'Migration système',
            'Optimisation réseau', 'Mise à jour logicielle'
        ];

        $stmt = $db->prepare("INSERT IGNORE INTO `services` (`nom`) VALUES (?)");
        foreach ($services as $service) {
            $stmt->execute([$service]);
        }
        $messages[] = "✅ Services par défaut ajoutés";

        // 7. Créer des vues pour les statistiques
        $db->exec("CREATE OR REPLACE VIEW `evaluation_stats` AS
            SELECT
                COUNT(*) as total_evaluations,
                AVG(qualite) as avg_qualite,
                AVG(delai) as avg_delai,
                AVG(communication) as avg_communication,
                COUNT(DISTINCT prestataire) as total_prestataires,
                COUNT(DISTINCT service) as total_services,
                AVG((qualite + delai + communication) / 3) as avg_global
            FROM evaluations");
        $messages[] = "✅ Vue 'evaluation_stats' créée";

        $db->exec("CREATE OR REPLACE VIEW `prestataire_ranking` AS
            SELECT
                prestataire,
                COUNT(*) as nb_evaluations,
                AVG(qualite) as avg_qualite,
                AVG(delai) as avg_delai,
                AVG(communication) as avg_communication,
                AVG((qualite + delai + communication) / 3) as avg_global,
                MAX(created_at) as derniere_evaluation
            FROM evaluations
            GROUP BY prestataire
            ORDER BY avg_global DESC, nb_evaluations DESC");
        $messages[] = "✅ Vue 'prestataire_ranking' créée";

        // 8. Insérer quelques évaluations de test (optionnel)
        $evaluations_test = [
            ['Entreprise A', 'Maintenance informatique', 5, 4, 5, 'Excellent service, très professionnel'],
            ['Entreprise B', 'Support technique', 4, 3, 4, 'Bon support mais délais un peu longs'],
            ['TechCorp Solutions', 'Développement web', 5, 5, 5, 'Parfait, livré dans les temps'],
            ['Fournisseur Alpha', 'Formation', 3, 4, 3, 'Formation correcte mais pourrait être améliorée']
        ];

        $stmt = $db->prepare("INSERT IGNORE INTO `evaluations` (prestataire, service, qualite, delai, communication, commentaires) VALUES (?, ?, ?, ?, ?, ?)");
        foreach ($evaluations_test as $eval) {
            $stmt->execute($eval);
        }
        $messages[] = "✅ Évaluations de test ajoutées";

    } catch (PDOException $e) {
        $success = false;
        $messages[] = "❌ Erreur: " . $e->getMessage();
    }

    return ['success' => $success, 'messages' => $messages];
}

// Exécuter l'installation si le script est appelé directement
if (basename($_SERVER['PHP_SELF']) === 'install_evaluation_system.php') {
    $result = installEvaluationSystem($db);

    header('Content-Type: text/html; charset=utf-8');
    echo "<!DOCTYPE html>
    <html lang='fr'>
    <head>
        <meta charset='UTF-8'>
        <meta name='viewport' content='width=device-width, initial-scale=1.0'>
        <title>Installation du Système d'Évaluation - Schluter Systems</title>
        <style>
            body { font-family: Arial, sans-serif; max-width: 800px; margin: 50px auto; padding: 20px; }
            .success { color: #28a745; }
            .error { color: #dc3545; }
            .message { margin: 10px 0; padding: 10px; border-radius: 5px; }
            .success-msg { background-color: #d4edda; border: 1px solid #c3e6cb; }
            .error-msg { background-color: #f8d7da; border: 1px solid #f5c6cb; }
            .btn { display: inline-block; padding: 10px 20px; background-color: #f57c00; color: white; text-decoration: none; border-radius: 5px; margin-top: 20px; }
        </style>
    </head>
    <body>
        <h1>Installation du Système d'Évaluation des Prestataires</h1>
        <h2>Schluter Systems</h2>";

    if ($result['success']) {
        echo "<div class='message success-msg'><h3 class='success'>✅ Installation réussie !</h3></div>";
    } else {
        echo "<div class='message error-msg'><h3 class='error'>❌ Erreur lors de l'installation</h3></div>";
    }

    echo "<h3>Détails de l'installation :</h3><ul>";
    foreach ($result['messages'] as $message) {
        $class = strpos($message, '✅') !== false ? 'success' : 'error';
        echo "<li class='$class'>$message</li>";
    }
    echo "</ul>";

    if ($result['success']) {
        echo "<a href='evaluation.php' class='btn'>Accéder au système d'évaluation</a>";
    }

    echo "</body></html>";
}
?>
