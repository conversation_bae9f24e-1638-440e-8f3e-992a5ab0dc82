<?php
// Inclusion des configurations et composants
require_once 'config/database.php';
require_once 'config/schluter-config.php';
require_once 'templates/components.php';

// Récupérer les statistiques
function getStats() {
    $db = getDB();
    $stats = [];

    try {
        // Total des articles en inventaire
        $inventory = $db->fetchOne("SELECT COALESCE(SUM(quantite), 0) as total_items, COUNT(*) as unique_items FROM inventaire");
        $stats['inventory'] = $inventory ?: ['total_items' => 0, 'unique_items' => 0];

        // Changements de poste du mois en cours et du mois précédent
        $changes = $db->fetchAll("SELECT
            COUNT(*) as total,
            type,
            MONTH(date_changement) as mois
        FROM changements_poste
        WHERE MONTH(date_changement) IN (MONTH(CURRENT_DATE()), MONTH(CURRENT_DATE() - INTERVAL 1 MONTH))
        GROUP BY type, MONTH(date_changement)");

        // Séparer les stats par mois
        $stats['changes'] = array_filter($changes, function($change) {
            return $change['mois'] == date('n');
        });
        $stats['changes_prev'] = array_filter($changes, function($change) {
            return $change['mois'] == date('n', strtotime('-1 month'));
        });

        // Nombre d'accusés de réception du mois
        $ar = $db->fetchOne("SELECT COUNT(*) as total FROM accuses_reception
                            WHERE MONTH(date_creation) = MONTH(CURRENT_DATE())");
        $stats['ar'] = $ar ?: ['total' => 0];

    } catch (Exception $e) {
        error_log("Erreur lors de la récupération des statistiques: " . $e->getMessage());
        // Valeurs par défaut en cas d'erreur
        $stats = [
            'inventory' => ['total_items' => 0, 'unique_items' => 0],
            'changes' => [],
            'changes_prev' => [],
            'ar' => ['total' => 0]
        ];
    }

    return $stats;
}

$stats = getStats();

// Définir les services IT
$itServices = [
    [
        'title' => 'Inventaire',
        'description' => 'Gérez l\'inventaire du matériel informatique',
        'url' => 'inventaire.php',
        'icon' => 'fas fa-warehouse'
    ],
    [
        'title' => 'Changements de Poste',
        'description' => 'Suivi des équipements en fin de vie',
        'url' => 'End_Of_Life.php',
        'icon' => 'fas fa-exchange-alt'
    ],
    [
        'title' => 'Accusés de Réception',
        'description' => 'Création et suivi des accusés de réception',
        'url' => 'accuse_reception.php',
        'icon' => 'fas fa-file-signature'
    ],
    [
        'title' => 'Administration',
        'description' => 'Gérez les paramètres du système',
        'url' => 'admin.php',
        'icon' => 'fas fa-cog'
    ]
];
?>

<?php
renderSchlutterHeader(
    'Portail Informatique - Schluter Systems',
    'Portail informatique de Schluter Systems pour la gestion des ressources informatiques.',
    ['css/schluter-unified.css', 'js/schluter-common.js', 'https://cdn.jsdelivr.net/npm/chart.js']
);

// Afficher la navigation
renderSchlutterNavigation($_SERVER['PHP_SELF']);

// Afficher la navigation spécialisée IT
renderSchlutterSubNavigation('it', $_SERVER['PHP_SELF']);

// Afficher la bannière avec statistiques
renderSchlutterBanner(
    'Portail Informatique',
    'Gestion des ressources informatiques et support technique'
);
?>

<!-- Section des statistiques -->
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mb-12">
    <div class="card-header animate-on-scroll" data-animate="slide-up">
        <div class="text-center">
            <h2 class="text-3xl font-bold mb-4">Statistiques du Service Informatique</h2>
            <p class="text-xl opacity-90">Aperçu de l'activité récente</p>
        </div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mt-8">
        <!-- Statistiques d'inventaire -->
        <div class="card text-center animate-on-scroll" data-animate="scale-in">
            <div class="bg-schluter-blue text-white p-4 rounded-xl mb-4 inline-block">
                <i class="fas fa-box text-2xl"></i>
            </div>
            <h3 class="text-3xl font-bold text-schluter-gray mb-2">
                <?= $stats['inventory']['total_items'] ?>
            </h3>
            <p class="text-schluter-gray-medium">Articles en inventaire</p>
            <p class="text-sm text-schluter-gray-medium mt-1">
                <?= $stats['inventory']['unique_items'] ?> articles uniques
            </p>
        </div>

        <!-- Changements de poste -->
        <div class="card text-center animate-on-scroll" data-animate="scale-in" style="animation-delay: 0.1s;">
            <div class="bg-schluter-orange text-white p-4 rounded-xl mb-4 inline-block">
                <i class="fas fa-exchange-alt text-2xl"></i>
            </div>
            <h3 class="text-3xl font-bold text-schluter-gray mb-2">
                <?= array_sum(array_column($stats['changes'], 'total')) ?>
            </h3>
            <p class="text-schluter-gray-medium">Changements ce mois</p>
            <p class="text-sm text-schluter-gray-medium mt-1">
                <?= array_sum(array_column($stats['changes_prev'], 'total')) ?> le mois dernier
            </p>
        </div>

        <!-- Accusés de réception -->
        <div class="card text-center animate-on-scroll" data-animate="scale-in" style="animation-delay: 0.2s;">
            <div class="bg-schluter-success text-white p-4 rounded-xl mb-4 inline-block">
                <i class="fas fa-file-signature text-2xl"></i>
            </div>
            <h3 class="text-3xl font-bold text-schluter-gray mb-2">
                <?= $stats['ar']['total'] ?>
            </h3>
            <p class="text-schluter-gray-medium">Accusés de réception</p>
            <p class="text-sm text-schluter-gray-medium mt-1">Ce mois-ci</p>
        </div>
    </div>
</div>

<!-- Grille des services IT -->
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mb-16">
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <?php foreach ($itServices as $index => $service): ?>
            <div class="animate-on-scroll" data-animate="scale-in" style="animation-delay: <?= $index * 0.1 ?>s;">
                <?php
                renderSchlutterServiceCard(
                    $service['title'],
                    $service['description'],
                    $service['url'],
                    $service['icon']
                );
                ?>
            </div>
        <?php endforeach; ?>
    </div>
</div>

<!-- Section graphiques (optionnelle) -->
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mb-16">
    <div class="card animate-on-scroll" data-animate="slide-up">
        <div class="flex items-center mb-6">
            <div class="bg-schluter-info text-white p-3 rounded-lg mr-4">
                <i class="fas fa-chart-bar text-xl"></i>
            </div>
            <h2 class="text-2xl font-semibold text-schluter-gray">Évolution des Activités</h2>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Graphique des changements de poste -->
            <div class="bg-schluter-background p-4 rounded-lg">
                <h3 class="text-lg font-semibold text-schluter-gray mb-4">Changements de Poste</h3>
                <canvas id="changesChart" width="400" height="200"></canvas>
            </div>

            <!-- Graphique de l'inventaire -->
            <div class="bg-schluter-background p-4 rounded-lg">
                <h3 class="text-lg font-semibold text-schluter-gray mb-4">Évolution Inventaire</h3>
                <canvas id="inventoryChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
</div>

<script>
// Initialisation des graphiques
document.addEventListener('DOMContentLoaded', function() {
    // Graphique des changements de poste
    const changesCtx = document.getElementById('changesChart').getContext('2d');
    new Chart(changesCtx, {
        type: 'doughnut',
        data: {
            labels: ['Arrivées', 'Départs', 'Mutations'],
            datasets: [{
                data: [
                    <?= array_sum(array_column(array_filter($stats['changes'], function($c) { return $c['type'] === 'arrivee'; }), 'total')) ?>,
                    <?= array_sum(array_column(array_filter($stats['changes'], function($c) { return $c['type'] === 'depart'; }), 'total')) ?>,
                    <?= array_sum(array_column(array_filter($stats['changes'], function($c) { return $c['type'] === 'mutation'; }), 'total')) ?>
                ],
                backgroundColor: ['#28a745', '#dc3545', '#f57c00']
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });

    // Graphique de l'inventaire (exemple)
    const inventoryCtx = document.getElementById('inventoryChart').getContext('2d');
    new Chart(inventoryCtx, {
        type: 'bar',
        data: {
            labels: ['Ordinateurs', 'Écrans', 'Périphériques', 'Serveurs'],
            datasets: [{
                label: 'Quantité',
                data: [45, 32, 28, 8], // Données d'exemple
                backgroundColor: '#005ea2'
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
});
</script>

<?php renderSchlutterFooter(); ?>