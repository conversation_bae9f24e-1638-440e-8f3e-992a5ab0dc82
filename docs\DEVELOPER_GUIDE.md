# Guide du Développeur - Portail Schluter Systems

## 🚀 Vue d'ensemble

Ce guide détaille l'architecture modernisée du portail intranet Schluter Systems, les nouveaux composants et les meilleures pratiques de développement.

## 📋 Table des Matières

1. [Architecture](#architecture)
2. [Configuration](#configuration)
3. [Composants](#composants)
4. [Base de Données](#base-de-données)
5. [Styles et Design](#styles-et-design)
6. [JavaScript](#javascript)
7. [Tests](#tests)
8. [Déploiement](#déploiement)

## 🏗️ Architecture

### Structure des Dossiers

```
/
├── config/                 # Configurations
│   ├── database.php        # Gestionnaire DB
│   ├── environment.php     # Config environnements
│   ├── schluter-config.php # Config globale
│   └── tailwind.config.js  # Config Tailwind
├── templates/              # Composants réutilisables
│   └── components.php      # Fonctions de rendu
├── css/                    # Styles
│   └── schluter-unified.css
├── js/                     # JavaScript
│   └── schluter-common.js
├── tests/                  # Tests système
├── docs/                   # Documentation
└── dist/                   # Assets compilés
```

### Patterns Utilisés

- **Singleton** : Gestion de la base de données
- **Factory** : Génération des composants
- **Configuration centralisée** : Environnements et paramètres
- **Composants réutilisables** : Templates modulaires

## ⚙️ Configuration

### Environnements

Le système supporte trois environnements :

```php
// Détection automatique
$env = detectEnvironment(); // 'development', 'testing', 'production'

// Configuration spécifique
$config = getEnvironmentConfig($env);
```

### Variables d'Environnement

```bash
# Définir l'environnement
export SCHLUTER_ENV=production

# Ou dans .htaccess
SetEnv SCHLUTER_ENV production
```

### Configuration Base de Données

```php
// config/environment.php
'database' => [
    'host' => 'localhost',
    'dbname' => 'schluter_db',
    'username' => 'user',
    'password' => 'password',
    'charset' => 'utf8mb4'
]
```

## 🧩 Composants

### Utilisation des Composants

```php
<?php
require_once 'config/database.php';
require_once 'config/schluter-config.php';
require_once 'templates/components.php';

// Header avec assets
renderSchluter Header(
    'Titre de la page',
    'Description SEO',
    ['css/custom.css', 'js/custom.js']
);

// Navigation
renderSchluter Navigation($_SERVER['PHP_SELF']);

// Bannière
renderSchluter Banner('Titre', 'Sous-titre');

// Carte de service
renderSchluter ServiceCard(
    'Titre du service',
    'Description du service',
    'url-du-service.php',
    'fas fa-icon'
);

// Footer
renderSchluter Footer();
?>
```

### Composants Disponibles

| Composant | Fonction | Description |
|-----------|----------|-------------|
| Header | `renderSchluter Header()` | En-tête HTML avec assets |
| Navigation | `renderSchluter Navigation()` | Barre de navigation |
| SubNavigation | `renderSchluter SubNavigation()` | Navigation secondaire |
| Banner | `renderSchluter Banner()` | Bannière avec gradient |
| ServiceCard | `renderSchluter ServiceCard()` | Carte de service |
| Table | `renderSchluter TableStart/End()` | Tableaux standardisés |
| Form | `renderSchluter FormStart/End()` | Formulaires |
| Alert | `renderSchluter Alert()` | Notifications |
| Footer | `renderSchluter Footer()` | Pied de page |

## 🗄️ Base de Données

### Utilisation

```php
// Obtenir l'instance
$db = getDB();

// Requêtes simples
$users = $db->fetchAll("SELECT * FROM users WHERE active = ?", [1]);
$user = $db->fetchOne("SELECT * FROM users WHERE id = ?", [123]);
$count = $db->fetchColumn("SELECT COUNT(*) FROM users");

// Insertion
$userId = $db->insert('users', [
    'name' => 'John Doe',
    'email' => '<EMAIL>',
    'created_at' => date('Y-m-d H:i:s')
]);

// Mise à jour
$db->update('users', 
    ['name' => 'Jane Doe'], 
    'id = :id', 
    ['id' => $userId]
);

// Suppression
$db->delete('users', 'id = :id', ['id' => $userId]);

// Transactions
$db->beginTransaction();
try {
    $db->insert('table1', $data1);
    $db->insert('table2', $data2);
    $db->commit();
} catch (Exception $e) {
    $db->rollback();
    throw $e;
}
```

### Méthodes Disponibles

| Méthode | Description | Retour |
|---------|-------------|--------|
| `fetchAll($sql, $params)` | Récupère tous les résultats | Array |
| `fetchOne($sql, $params)` | Récupère un résultat | Array/null |
| `fetchColumn($sql, $params)` | Récupère une valeur | Mixed |
| `insert($table, $data)` | Insère des données | Last insert ID |
| `update($table, $data, $where, $params)` | Met à jour | PDOStatement |
| `delete($table, $where, $params)` | Supprime | PDOStatement |
| `executeQuery($sql, $params)` | Exécute une requête | PDOStatement |

## 🎨 Styles et Design

### Système de Couleurs

```css
:root {
  /* Couleurs principales */
  --schluter-orange: #f57c00;
  --schluter-blue: #005ea2;
  
  /* Couleurs système */
  --schluter-success: #28a745;
  --schluter-warning: #ffc107;
  --schluter-danger: #dc3545;
  --schluter-info: #17a2b8;
}
```

### Classes Utilitaires

```css
/* Composants */
.card                 /* Carte standardisée */
.card-header         /* En-tête avec gradient */
.btn-primary         /* Bouton principal */
.btn-secondary       /* Bouton secondaire */
.form-input          /* Champ de formulaire */

/* Couleurs */
.text-schluter-orange
.bg-schluter-blue
.border-schluter-success

/* Animations */
.animate-fade-in     /* Apparition en fondu */
.animate-slide-up    /* Glissement vers le haut */
.animate-scale-in    /* Zoom d'entrée */
.hover-lift          /* Élévation au survol */
.hover-scale         /* Agrandissement au survol */

/* Utilitaires */
.gradient-schluter   /* Gradient Schluter */
.glass-effect        /* Effet de verre */
```

### Responsive Design

```css
/* Mobile First */
.grid-cols-1         /* 1 colonne par défaut */
.md:grid-cols-2      /* 2 colonnes sur tablette */
.lg:grid-cols-3      /* 3 colonnes sur desktop */

/* Breakpoints */
/* sm: 640px */
/* md: 768px */
/* lg: 1024px */
/* xl: 1280px */
```

## 📱 JavaScript

### Fonctionnalités Communes

```javascript
// Objet global
SchlutterPortal.init(); // Initialisation automatique

// Notifications
SchlutterPortal.showNotification('Message', 'success', 5000);

// Requêtes AJAX
const data = await SchlutterPortal.makeRequest('/api/endpoint', {
    method: 'POST',
    body: JSON.stringify(payload)
});

// Validation de formulaires
SchlutterPortal.validateForm(formElement);
SchlutterPortal.validateField(inputElement);
```

### Animations

```javascript
// Animation au scroll
<div class="animate-on-scroll" data-animate="slide-up">
    Contenu animé
</div>

// Animation manuelle
element.classList.add('animate-fade-in');
```

### Événements Personnalisés

```javascript
// Recherche en temps réel
<input data-search=".searchable-item" placeholder="Rechercher...">

// Tooltips
<button data-tooltip="Information utile">Bouton</button>
```

## 🧪 Tests

### Exécution des Tests

```bash
# Tests système complets
php tests/system-test.php

# Tests spécifiques
php tests/database-test.php
php tests/components-test.php
```

### Écriture de Tests

```php
class MonTest extends SchlutterSystemTest {
    public function testMaFonctionnalite() {
        $this->assert(
            condition_a_tester(),
            "Message d'erreur si échec"
        );
    }
}
```

## 🚀 Déploiement

### Script de Déploiement

```bash
# Déploiement automatique
php deploy.php

# Compilation manuelle
npm install
npm run build

# Tests avant déploiement
php tests/system-test.php
```

### Checklist de Déploiement

- [ ] Tests système passés
- [ ] Assets compilés et minifiés
- [ ] Configuration de production
- [ ] Sauvegarde créée
- [ ] Base de données migrée
- [ ] Permissions fichiers correctes

## 📝 Bonnes Pratiques

### Code PHP

```php
// Toujours utiliser les composants
require_once 'templates/components.php';

// Gestion d'erreurs
try {
    $result = $db->fetchAll($sql, $params);
} catch (Exception $e) {
    logError("Erreur: " . $e->getMessage());
    // Gestion appropriée
}

// Validation des entrées
$input = filter_input(INPUT_POST, 'field', FILTER_SANITIZE_STRING);
if (empty($input)) {
    throw new InvalidArgumentException("Champ requis");
}
```

### CSS

```css
/* Utiliser les variables CSS */
.mon-composant {
    color: var(--schluter-orange);
    background: var(--schluter-surface);
}

/* Classes utilitaires plutôt que CSS personnalisé */
<div class="card hover-lift animate-scale-in">
```

### JavaScript

```javascript
// Utiliser les fonctions communes
SchlutterPortal.showNotification('Succès!', 'success');

// Event delegation
document.addEventListener('click', function(e) {
    if (e.target.matches('.mon-bouton')) {
        // Action
    }
});
```

## 🔧 Dépannage

### Problèmes Courants

1. **Erreur de connexion DB**
   - Vérifier `config/environment.php`
   - Contrôler les credentials

2. **Assets non chargés**
   - Exécuter `npm run build`
   - Vérifier les chemins

3. **Composants non rendus**
   - Inclure `templates/components.php`
   - Vérifier la syntaxe PHP

### Logs

```bash
# Logs d'erreurs
tail -f error.log

# Logs de déploiement
tail -f deployment.log

# Logs personnalisés (développement)
tail -f php_errors.log
```

## 📞 Support

- **Documentation** : `/docs/`
- **Tests** : `php tests/system-test.php`
- **Issues** : Créer un ticket GitHub
- **Contact** : <EMAIL>

---

**Version** : 2.0.0  
**Dernière mise à jour** : Décembre 2024
