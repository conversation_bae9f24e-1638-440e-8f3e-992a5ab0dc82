<?php
/**
 * Test simple de la table services
 */

// Démarrer la session avant d'inclure les configurations
session_start();

// Inclure les configurations
require_once 'config/database.php';

try {
    echo "<h2>Test de la table services</h2>";
    
    // Obtenir l'instance de la base de données
    $db = getDB();
    
    echo "✅ Connexion à la base de données réussie<br>";
    
    // Tester la requête qui pose problème
    $services = $db->fetchAll('SELECT title, description, icon FROM services');
    
    echo "✅ Requête services exécutée avec succès<br>";
    echo "Nombre de services trouvés: " . count($services) . "<br><br>";
    
    if (!empty($services)) {
        echo "<h3>Services disponibles:</h3>";
        echo "<ul>";
        foreach ($services as $service) {
            echo "<li><strong>" . htmlspecialchars($service['title']) . "</strong>: " . htmlspecialchars($service['description']) . "</li>";
        }
        echo "</ul>";
    } else {
        echo "⚠️ Aucun service trouvé dans la base de données<br>";
    }
    
    echo "<br><a href='index.php'>Tester l'application complète</a>";
    
} catch (Exception $e) {
    echo "❌ Erreur: " . $e->getMessage() . "<br>";
    echo "Fichier: " . $e->getFile() . "<br>";
    echo "Ligne: " . $e->getLine() . "<br>";
}
?>
