/**
 * Fonctionnalités JavaScript communes pour le portail Schluter Systems
 * Inclut les animations, validations et utilitaires
 */

// Configuration globale
const SchlutterPortal = {
    config: {
        animationDuration: 300,
        debounceDelay: 300,
        apiTimeout: 10000
    },
    
    // Initialisation au chargement de la page
    init() {
        this.setupEventListeners();
        this.initializeAnimations();
        this.setupFormValidation();
        this.initializeTooltips();
        this.setupBackToTop();
        this.initializeMobileMenu();
    },
    
    // Configuration des écouteurs d'événements
    setupEventListeners() {
        document.addEventListener('DOMContentLoaded', () => {
            this.animateOnScroll();
            this.setupSearchFunctionality();
        });
        
        window.addEventListener('scroll', this.debounce(() => {
            this.handleScroll();
        }, 100));
        
        window.addEventListener('resize', this.debounce(() => {
            this.handleResize();
        }, 250));
    },
    
    // Initialisation des animations
    initializeAnimations() {
        // Animation des cartes au chargement
        const cards = document.querySelectorAll('.card');
        cards.forEach((card, index) => {
            card.style.animationDelay = `${index * 0.1}s`;
            card.classList.add('animate-scale-in');
        });
        
        // Animation des éléments au scroll
        this.observeElements();
    },
    
    // Observer pour les animations au scroll
    observeElements() {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-slide-up');
                    observer.unobserve(entry.target);
                }
            });
        }, {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        });
        
        // Observer les éléments avec la classe 'animate-on-scroll'
        document.querySelectorAll('.animate-on-scroll').forEach(el => {
            observer.observe(el);
        });
    },
    
    // Animation au scroll
    animateOnScroll() {
        const elements = document.querySelectorAll('[data-animate]');
        
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const animationType = entry.target.dataset.animate;
                    entry.target.classList.add(`animate-${animationType}`);
                }
            });
        });
        
        elements.forEach(el => observer.observe(el));
    },
    
    // Gestion du scroll
    handleScroll() {
        const scrollY = window.scrollY;
        
        // Parallax effect pour les bannières
        const banners = document.querySelectorAll('.gradient-schluter');
        banners.forEach(banner => {
            const speed = 0.5;
            banner.style.transform = `translateY(${scrollY * speed}px)`;
        });
        
        // Affichage/masquage du bouton retour en haut
        const backToTop = document.getElementById('backToTop');
        if (backToTop) {
            if (scrollY > 300) {
                backToTop.classList.remove('opacity-0', 'invisible');
                backToTop.classList.add('opacity-100', 'visible');
            } else {
                backToTop.classList.add('opacity-0', 'invisible');
                backToTop.classList.remove('opacity-100', 'visible');
            }
        }
    },
    
    // Gestion du redimensionnement
    handleResize() {
        // Réajustement des éléments responsives si nécessaire
        this.adjustMobileMenu();
    },
    
    // Configuration du bouton retour en haut
    setupBackToTop() {
        const backToTop = document.getElementById('backToTop');
        if (backToTop) {
            backToTop.addEventListener('click', () => {
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            });
        }
    },
    
    // Initialisation du menu mobile
    initializeMobileMenu() {
        const mobileMenuButton = document.getElementById('mobile-menu-button');
        const mobileMenu = document.getElementById('mobile-menu');
        
        if (mobileMenuButton && mobileMenu) {
            mobileMenuButton.addEventListener('click', () => {
                mobileMenu.classList.toggle('hidden');
                
                // Animation de l'icône hamburger
                const icon = mobileMenuButton.querySelector('i');
                if (icon) {
                    icon.classList.toggle('fa-bars');
                    icon.classList.toggle('fa-times');
                }
            });
        }
    },
    
    // Ajustement du menu mobile
    adjustMobileMenu() {
        const mobileMenu = document.getElementById('mobile-menu');
        if (window.innerWidth > 768 && mobileMenu) {
            mobileMenu.classList.add('hidden');
        }
    },
    
    // Configuration de la validation des formulaires
    setupFormValidation() {
        const forms = document.querySelectorAll('form[data-validate]');
        
        forms.forEach(form => {
            form.addEventListener('submit', (e) => {
                if (!this.validateForm(form)) {
                    e.preventDefault();
                }
            });
            
            // Validation en temps réel
            const inputs = form.querySelectorAll('input, textarea, select');
            inputs.forEach(input => {
                input.addEventListener('blur', () => {
                    this.validateField(input);
                });
                
                input.addEventListener('input', this.debounce(() => {
                    this.validateField(input);
                }, this.config.debounceDelay));
            });
        });
    },
    
    // Validation d'un formulaire
    validateForm(form) {
        let isValid = true;
        const inputs = form.querySelectorAll('input[required], textarea[required], select[required]');
        
        inputs.forEach(input => {
            if (!this.validateField(input)) {
                isValid = false;
            }
        });
        
        return isValid;
    },
    
    // Validation d'un champ
    validateField(field) {
        const value = field.value.trim();
        const type = field.type;
        let isValid = true;
        let message = '';
        
        // Vérification des champs requis
        if (field.hasAttribute('required') && !value) {
            isValid = false;
            message = 'Ce champ est requis';
        }
        
        // Validation par type
        if (value && type === 'email') {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(value)) {
                isValid = false;
                message = 'Adresse email invalide';
            }
        }
        
        if (value && type === 'tel') {
            const phoneRegex = /^[\d\s\-\+\(\)]+$/;
            if (!phoneRegex.test(value)) {
                isValid = false;
                message = 'Numéro de téléphone invalide';
            }
        }
        
        // Affichage du résultat
        this.showFieldValidation(field, isValid, message);
        return isValid;
    },
    
    // Affichage de la validation d'un champ
    showFieldValidation(field, isValid, message) {
        // Suppression des classes précédentes
        field.classList.remove('border-green-500', 'border-red-500');
        
        // Suppression du message d'erreur précédent
        const existingError = field.parentNode.querySelector('.error-message');
        if (existingError) {
            existingError.remove();
        }
        
        if (isValid) {
            field.classList.add('border-green-500');
        } else {
            field.classList.add('border-red-500');
            
            // Ajout du message d'erreur
            const errorDiv = document.createElement('div');
            errorDiv.className = 'error-message text-red-500 text-sm mt-1';
            errorDiv.textContent = message;
            field.parentNode.appendChild(errorDiv);
        }
    },
    
    // Configuration de la fonctionnalité de recherche
    setupSearchFunctionality() {
        const searchInputs = document.querySelectorAll('[data-search]');
        
        searchInputs.forEach(input => {
            const target = input.dataset.search;
            const searchableElements = document.querySelectorAll(target);
            
            input.addEventListener('input', this.debounce(() => {
                this.performSearch(input.value, searchableElements);
            }, this.config.debounceDelay));
        });
    },
    
    // Exécution de la recherche
    performSearch(query, elements) {
        const searchTerm = query.toLowerCase().trim();
        
        elements.forEach(element => {
            const text = element.textContent.toLowerCase();
            const isVisible = !searchTerm || text.includes(searchTerm);
            
            element.style.display = isVisible ? '' : 'none';
            
            // Animation de fade
            if (isVisible) {
                element.classList.add('animate-fade-in');
            }
        });
    },
    
    // Initialisation des tooltips
    initializeTooltips() {
        const tooltipElements = document.querySelectorAll('[data-tooltip]');
        
        tooltipElements.forEach(element => {
            element.addEventListener('mouseenter', (e) => {
                this.showTooltip(e.target);
            });
            
            element.addEventListener('mouseleave', () => {
                this.hideTooltip();
            });
        });
    },
    
    // Affichage d'un tooltip
    showTooltip(element) {
        const text = element.dataset.tooltip;
        const tooltip = document.createElement('div');
        
        tooltip.className = 'tooltip fixed bg-gray-800 text-white px-2 py-1 rounded text-sm z-50 pointer-events-none';
        tooltip.textContent = text;
        tooltip.id = 'active-tooltip';
        
        document.body.appendChild(tooltip);
        
        // Positionnement
        const rect = element.getBoundingClientRect();
        tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
        tooltip.style.top = rect.top - tooltip.offsetHeight - 5 + 'px';
    },
    
    // Masquage du tooltip
    hideTooltip() {
        const tooltip = document.getElementById('active-tooltip');
        if (tooltip) {
            tooltip.remove();
        }
    },
    
    // Fonction utilitaire de debounce
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },
    
    // Affichage de notifications
    showNotification(message, type = 'info', duration = 5000) {
        const notification = document.createElement('div');
        const typeClasses = {
            success: 'bg-green-500',
            error: 'bg-red-500',
            warning: 'bg-yellow-500',
            info: 'bg-blue-500'
        };
        
        notification.className = `fixed top-4 right-4 ${typeClasses[type]} text-white px-6 py-4 rounded-lg shadow-lg z-50 animate-slide-up`;
        notification.innerHTML = `
            <div class="flex items-center justify-between">
                <span>${message}</span>
                <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-white hover:text-gray-200">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        // Suppression automatique
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, duration);
    },
    
    // Utilitaire pour les requêtes AJAX
    async makeRequest(url, options = {}) {
        const defaultOptions = {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
            },
            timeout: this.config.apiTimeout
        };
        
        const config = { ...defaultOptions, ...options };
        
        try {
            const response = await fetch(url, config);
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            return await response.json();
        } catch (error) {
            console.error('Request failed:', error);
            this.showNotification('Une erreur est survenue lors de la requête', 'error');
            throw error;
        }
    }
};

// Initialisation automatique
document.addEventListener('DOMContentLoaded', () => {
    SchlutterPortal.init();
});

// Export pour utilisation dans d'autres scripts
window.SchlutterPortal = SchlutterPortal;
