<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

function getDbConnection() {
    try {
        $conn = new PDO("mysql:host=localhost;dbname=schluter_db", "root", "");
        $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        return $conn;
    } catch(PDOException $e) {
        die("Erreur de connexion : " . $e->getMessage());
    }
}

// Récupération de tous les accusés de réception avec statistiques
$conn = getDbConnection();
$sql = "SELECT id as ID, collaborateur as Collaborateur, responsable as Responsable,
        date_creation as Date, materiel_remis as Remis, materiel_recu as Recu
        FROM accuses_reception
        ORDER BY date_creation DESC";
$stmt = $conn->query($sql);
$arData = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Calcul des statistiques
$totalAccuses = count($arData);
$thisMonth = array_filter($arData, function($ar) {
    return date('Y-m', strtotime($ar['Date'])) === date('Y-m');
});
$thisYear = array_filter($arData, function($ar) {
    return date('Y', strtotime($ar['Date'])) === date('Y');
});

// Statistiques par mois pour les 12 derniers mois
$monthlyStats = [];
$monthlyLabels = [];
$monthlyCounts = [];

for ($i = 11; $i >= 0; $i--) {
    $month = date('Y-m', strtotime("-$i months"));
    $monthLabel = date('M Y', strtotime("-$i months"));

    $monthData = array_filter($arData, function($ar) use ($month) {
        return date('Y-m', strtotime($ar['Date'])) === $month;
    });

    $monthlyStats[$month] = $monthData;
    $monthlyLabels[] = $monthLabel;
    $monthlyCounts[] = count($monthData);
}

// Top collaborateurs et responsables
$collaborateurs = array_count_values(array_column($arData, 'Collaborateur'));
$responsables = array_count_values(array_column($arData, 'Responsable'));
arsort($collaborateurs);
arsort($responsables);

?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Historique complet des accusés de réception - Schluter Systems">
    <title>Historique des Accusés de Réception - Schluter Systems</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        schluter: {
                            orange: '#f57c00',
                            'orange-dark': '#e65100',
                            blue: '#005ea2',
                            'blue-dark': '#004c87',
                            gray: '#333333',
                            'gray-light': '#f4f4f4'
                        }
                    }
                }
            }
        }
    </script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <!-- Chart.js avec fallback -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.js"></script>
    <script>
        // Fallback pour Chart.js si le premier CDN échoue
        if (typeof Chart === 'undefined') {
            document.write('<script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/4.4.0/chart.umd.js"><\/script>');
        }
    </script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-gray-100">
    <!-- Barre de navigation adaptée pour Tailwind -->
    <header class="fixed top-0 left-0 w-full bg-schluter-gray shadow-lg z-50">
        <div class="flex items-center justify-between px-6 py-4">
            <a href="IT.php" class="flex items-center gap-3 text-white text-xl font-bold hover:text-schluter-orange transition-colors duration-200">
                <img src="img/logo_rgb.svg" alt="Schluter Systems Logo" class="h-10">
                Informatique
            </a>
            <nav class="flex gap-6">
                <a href="accuse_reception.php" class="text-white hover:bg-schluter-orange px-4 py-2 rounded-lg transition-all duration-200 <?= basename($_SERVER['PHP_SELF']) === 'accuse_reception.php' ? 'bg-schluter-orange' : '' ?>">
                    Accusés de Réception
                </a>
                <a href="End_Of_Life.php" class="text-white hover:bg-schluter-orange px-4 py-2 rounded-lg transition-all duration-200 <?= basename($_SERVER['PHP_SELF']) === 'End_Of_Life.php' ? 'bg-schluter-orange' : '' ?>">
                    Changements de Poste
                </a>
                <a href="inventaire.php" class="text-white hover:bg-schluter-orange px-4 py-2 rounded-lg transition-all duration-200 <?= basename($_SERVER['PHP_SELF']) === 'inventaire.php' ? 'bg-schluter-orange' : '' ?>">
                    Inventaire
                </a>
            </nav>
        </div>
    </header>

    <main class="container mx-auto px-4 py-8 mt-20">
        <!-- En-tête avec gradient et statistiques -->
        <div class="bg-gradient-to-r from-schluter-blue to-schluter-orange rounded-lg shadow-lg p-8 mb-8 text-white">
            <div class="flex justify-between items-center mb-6">
                <h1 class="text-4xl font-bold">Historique des Accusés de Réception</h1>
                <div class="flex items-center gap-4">
                    <a href="accuse_reception.php" class="bg-white/20 hover:bg-white/30 backdrop-blur-sm text-white px-6 py-3 rounded-lg transition-all duration-300 flex items-center gap-2">
                        <i class="fas fa-plus"></i>
                        Créer un Accusé
                    </a>
                    <button onclick="exportToCSV()" class="bg-white/20 hover:bg-white/30 backdrop-blur-sm text-white px-6 py-3 rounded-lg transition-all duration-300 flex items-center gap-2">
                        <i class="fas fa-file-csv"></i>
                        Exporter CSV
                    </button>
                </div>
            </div>

            <!-- Statistiques rapides -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div class="bg-white/20 rounded-lg p-6 backdrop-blur-sm">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-lg opacity-90">Total Historique</p>
                            <h3 class="text-3xl font-bold"><?= $totalAccuses ?></h3>
                        </div>
                        <i class="fas fa-archive text-4xl opacity-75"></i>
                    </div>
                    <p class="mt-2 text-sm opacity-75">Tous les accusés</p>
                </div>

                <div class="bg-white/20 rounded-lg p-6 backdrop-blur-sm">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-lg opacity-90">Ce Mois</p>
                            <h3 class="text-3xl font-bold"><?= count($thisMonth) ?></h3>
                        </div>
                        <i class="fas fa-calendar-month text-4xl opacity-75"></i>
                    </div>
                    <p class="mt-2 text-sm opacity-75"><?= date('F Y') ?></p>
                </div>

                <div class="bg-white/20 rounded-lg p-6 backdrop-blur-sm">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-lg opacity-90">Cette Année</p>
                            <h3 class="text-3xl font-bold"><?= count($thisYear) ?></h3>
                        </div>
                        <i class="fas fa-calendar-year text-4xl opacity-75"></i>
                    </div>
                    <p class="mt-2 text-sm opacity-75"><?= date('Y') ?></p>
                </div>

                <div class="bg-white/20 rounded-lg p-6 backdrop-blur-sm">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-lg opacity-90">Moyenne/Mois</p>
                            <h3 class="text-3xl font-bold"><?= $totalAccuses > 0 ? round($totalAccuses / max(1, count($monthlyStats))) : 0 ?></h3>
                        </div>
                        <i class="fas fa-chart-line text-4xl opacity-75"></i>
                    </div>
                    <p class="mt-2 text-sm opacity-75">Sur 12 mois</p>
                </div>
            </div>
        </div>

        <!-- Section d'analyse et graphiques -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <!-- Graphique des tendances mensuelles -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <div class="flex items-center mb-4">
                    <div class="bg-schluter-blue text-white p-3 rounded-lg mr-4">
                        <i class="fas fa-chart-line text-xl"></i>
                    </div>
                    <h3 class="text-xl font-semibold text-schluter-blue">Tendances Mensuelles</h3>
                </div>
                <div class="relative h-64">
                    <canvas id="monthlyChart"></canvas>
                </div>
                <?php if (empty($arData)): ?>
                    <div class="text-center text-gray-500 mt-4">
                        <i class="fas fa-chart-line text-4xl mb-2"></i>
                        <p>Aucune donnée à afficher</p>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Top collaborateurs et responsables -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <div class="flex items-center mb-4">
                    <div class="bg-schluter-orange text-white p-3 rounded-lg mr-4">
                        <i class="fas fa-users text-xl"></i>
                    </div>
                    <h3 class="text-xl font-semibold text-schluter-blue">Top Utilisateurs</h3>
                </div>

                <div class="space-y-4">
                    <div>
                        <h4 class="font-semibold text-gray-700 mb-2">
                            <i class="fas fa-user mr-2 text-schluter-blue"></i>Top Collaborateurs
                        </h4>
                        <div class="space-y-2">
                            <?php $count = 0; foreach ($collaborateurs as $nom => $total): if ($count >= 5) break; ?>
                                <div class="flex justify-between items-center bg-gray-50 p-2 rounded">
                                    <span class="text-sm font-medium"><?= htmlspecialchars($nom) ?></span>
                                    <span class="bg-schluter-blue text-white px-2 py-1 rounded-full text-xs"><?= $total ?></span>
                                </div>
                            <?php $count++; endforeach; ?>
                        </div>
                    </div>

                    <div>
                        <h4 class="font-semibold text-gray-700 mb-2">
                            <i class="fas fa-user-tie mr-2 text-schluter-orange"></i>Top Responsables
                        </h4>
                        <div class="space-y-2">
                            <?php $count = 0; foreach ($responsables as $nom => $total): if ($count >= 5) break; ?>
                                <div class="flex justify-between items-center bg-gray-50 p-2 rounded">
                                    <span class="text-sm font-medium"><?= htmlspecialchars($nom) ?></span>
                                    <span class="bg-schluter-orange text-white px-2 py-1 rounded-full text-xs"><?= $total ?></span>
                                </div>
                            <?php $count++; endforeach; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Section de recherche et filtres -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
            <div class="flex items-center mb-4">
                <div class="bg-schluter-orange text-white p-3 rounded-lg mr-4">
                    <i class="fas fa-search text-xl"></i>
                </div>
                <h3 class="text-xl font-semibold text-schluter-blue">Recherche et Filtres</h3>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Recherche globale</label>
                    <input type="text" id="searchInput" placeholder="Rechercher..."
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-schluter-orange focus:border-transparent">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Collaborateur</label>
                    <select id="collaborateurFilter" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-schluter-orange focus:border-transparent">
                        <option value="">Tous les collaborateurs</option>
                        <?php foreach (array_keys($collaborateurs) as $collaborateur): ?>
                            <option value="<?= htmlspecialchars($collaborateur) ?>"><?= htmlspecialchars($collaborateur) ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Responsable</label>
                    <select id="responsableFilter" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-schluter-orange focus:border-transparent">
                        <option value="">Tous les responsables</option>
                        <?php foreach (array_keys($responsables) as $responsable): ?>
                            <option value="<?= htmlspecialchars($responsable) ?>"><?= htmlspecialchars($responsable) ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Période</label>
                    <select id="periodeFilter" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-schluter-orange focus:border-transparent">
                        <option value="">Toutes les périodes</option>
                        <option value="today">Aujourd'hui</option>
                        <option value="week">Cette semaine</option>
                        <option value="month">Ce mois</option>
                        <option value="year">Cette année</option>
                    </select>
                </div>
            </div>

            <div class="mt-4 flex gap-2">
                <button onclick="resetFilters()" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                    <i class="fas fa-undo mr-2"></i>Réinitialiser
                </button>
                <button onclick="applyFilters()" class="bg-schluter-blue hover:bg-schluter-blue-dark text-white px-4 py-2 rounded-lg transition-colors duration-200">
                    <i class="fas fa-filter mr-2"></i>Appliquer les Filtres
                </button>
            </div>
        </div>

        <!-- Section du tableau d'historique -->
        <div class="bg-white rounded-lg shadow-lg overflow-hidden">
            <div class="bg-gradient-to-r from-schluter-blue to-schluter-orange p-6">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <i class="fas fa-history text-white text-2xl mr-3"></i>
                        <h2 class="text-2xl font-bold text-white">Historique Complet</h2>
                    </div>
                    <div class="text-white text-sm opacity-90">
                        <span id="filteredCount"><?= count($arData) ?></span> / <?= count($arData) ?> accusé(s)
                    </div>
                </div>
            </div>

            <div class="overflow-x-auto">
                <?php if (empty($arData)): ?>
                    <div class="p-8 text-center">
                        <i class="fas fa-archive text-gray-400 text-6xl mb-4"></i>
                        <h3 class="text-xl font-semibold text-gray-600 mb-2">Aucun historique disponible</h3>
                        <p class="text-gray-500">L'historique des accusés de réception apparaîtra ici.</p>
                    </div>
                <?php else: ?>
                    <table class="w-full" id="historyTable">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100" onclick="sortTable(0)">
                                    <i class="fas fa-user mr-2"></i>Collaborateur
                                    <i class="fas fa-sort text-xs ml-1"></i>
                                </th>
                                <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100" onclick="sortTable(1)">
                                    <i class="fas fa-user-tie mr-2"></i>Responsable
                                    <i class="fas fa-sort text-xs ml-1"></i>
                                </th>
                                <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100" onclick="sortTable(2)">
                                    <i class="fas fa-calendar mr-2"></i>Date
                                    <i class="fas fa-sort text-xs ml-1"></i>
                                </th>
                                <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    <i class="fas fa-hand-holding mr-2"></i>Matériel Remis
                                </th>
                                <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    <i class="fas fa-inbox mr-2"></i>Matériel Reçu
                                </th>
                                <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    <i class="fas fa-cog mr-2"></i>Actions
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200" id="tableBody">
                            <?php foreach ($arData as $index => $ar): ?>
                                <tr class="hover:bg-gray-50 transition-colors duration-200 table-row"
                                    data-collaborateur="<?= htmlspecialchars($ar['Collaborateur']) ?>"
                                    data-responsable="<?= htmlspecialchars($ar['Responsable']) ?>"
                                    data-date="<?= $ar['Date'] ?>"
                                    data-remis="<?= htmlspecialchars($ar['Remis']) ?>"
                                    data-recu="<?= htmlspecialchars($ar['Recu']) ?>">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                        <div class="flex items-center">
                                            <div class="bg-schluter-blue text-white p-2 rounded-full mr-3">
                                                <i class="fas fa-user text-sm"></i>
                                            </div>
                                            <div>
                                                <div class="font-medium"><?= htmlspecialchars($ar['Collaborateur']) ?></div>
                                                <div class="text-xs text-gray-500">ID: <?= $ar['ID'] ?></div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                                        <div class="flex items-center">
                                            <div class="bg-schluter-orange text-white p-2 rounded-full mr-3">
                                                <i class="fas fa-user-tie text-sm"></i>
                                            </div>
                                            <?= htmlspecialchars($ar['Responsable']) ?>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <div class="flex items-center">
                                            <i class="fas fa-calendar text-schluter-blue mr-2"></i>
                                            <div>
                                                <div class="font-medium"><?= date('d/m/Y', strtotime($ar['Date'])) ?></div>
                                                <div class="text-xs text-gray-500"><?= date('H:i', strtotime($ar['Date'])) ?></div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 text-sm text-gray-600 max-w-xs">
                                        <div class="truncate" title="<?= htmlspecialchars($ar['Remis']) ?>">
                                            <span class="bg-green-100 text-green-800 px-2 py-1 rounded-lg text-xs">
                                                <?= strlen($ar['Remis']) > 40 ? substr(htmlspecialchars($ar['Remis']), 0, 40) . '...' : htmlspecialchars($ar['Remis']) ?>
                                            </span>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 text-sm text-gray-600 max-w-xs">
                                        <div class="truncate" title="<?= htmlspecialchars($ar['Recu']) ?>">
                                            <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded-lg text-xs">
                                                <?= strlen($ar['Recu']) > 40 ? substr(htmlspecialchars($ar['Recu']), 0, 40) . '...' : htmlspecialchars($ar['Recu']) ?>
                                            </span>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm">
                                        <div class="flex items-center gap-2">
                                            <button onclick="viewDetails(<?= $ar['ID'] ?>)"
                                                    class="bg-schluter-blue hover:bg-schluter-blue-dark text-white px-3 py-1 rounded-lg transition-colors duration-200 flex items-center gap-1"
                                                    title="Voir les détails">
                                                <i class="fas fa-eye text-xs"></i>
                                                Détails
                                            </button>
                                            <a href="accuse_reception.php?download_single_pdf=<?= $ar['ID'] ?>"
                                               class="bg-green-500 hover:bg-green-600 text-white px-3 py-1 rounded-lg transition-colors duration-200 flex items-center gap-1"
                                               title="Télécharger le PDF">
                                                <i class="fas fa-download text-xs"></i>
                                                PDF
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                <?php endif; ?>
            </div>
        </div>
    </main>

    <!-- Modal pour les détails -->
    <div id="detailsModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center">
        <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div class="bg-gradient-to-r from-schluter-blue to-schluter-orange p-6 text-white">
                <div class="flex justify-between items-center">
                    <h3 class="text-xl font-bold">Détails de l'Accusé de Réception</h3>
                    <button onclick="closeModal()" class="text-white hover:text-gray-200">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
            </div>
            <div id="modalContent" class="p-6">
                <!-- Le contenu sera injecté ici -->
            </div>
        </div>
    </div>

    <!-- Scripts d'animation et fonctionnalités -->
    <script>
        // Données pour les graphiques - Correction de la structure
        const monthlyLabels = <?= json_encode($monthlyLabels) ?>;
        const monthlyCounts = <?= json_encode($monthlyCounts) ?>;
        const allData = <?= json_encode($arData) ?>;
        let filteredData = [...allData];

        // Initialisation du graphique
        document.addEventListener('DOMContentLoaded', () => {
            // Attendre un peu pour s'assurer que Chart.js est chargé
            setTimeout(() => {
                if (typeof Chart !== 'undefined') {
                    initChart();
                } else {
                    console.error('Chart.js n\'est pas chargé correctement');
                    showChartError();
                }
            }, 100);

            // Animations GSAP
            gsap.from('.bg-gradient-to-r', {
                duration: 0.8,
                scale: 0.95,
                opacity: 0,
                ease: 'power2.out'
            });

            gsap.from('.bg-white', {
                duration: 0.6,
                y: 50,
                opacity: 0,
                stagger: 0.2,
                ease: 'power2.out',
                delay: 0.3
            });

            gsap.from('.table-row', {
                duration: 0.4,
                x: -20,
                opacity: 0,
                stagger: 0.02,
                ease: 'power2.out',
                delay: 1
            });

            // Recherche en temps réel
            document.getElementById('searchInput').addEventListener('input', applyFilters);
        });

        function initChart() {
            const ctx = document.getElementById('monthlyChart');
            if (!ctx) {
                console.error('Canvas monthlyChart non trouvé');
                return;
            }

            // Vérifier s'il y a des données
            if (!monthlyLabels || monthlyLabels.length === 0) {
                console.warn('Aucune donnée disponible pour le graphique');
                return;
            }

            try {
                new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: monthlyLabels,
                        datasets: [{
                            label: 'Accusés de réception',
                            data: monthlyCounts,
                            borderColor: '#f57c00',
                            backgroundColor: 'rgba(245, 124, 0, 0.1)',
                            borderWidth: 3,
                            fill: true,
                            tension: 0.4,
                            pointBackgroundColor: '#f57c00',
                            pointBorderColor: '#ffffff',
                            pointBorderWidth: 2,
                            pointRadius: 6,
                            pointHoverRadius: 8
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        interaction: {
                            intersect: false,
                            mode: 'index'
                        },
                        plugins: {
                            legend: {
                                display: false
                            },
                            tooltip: {
                                backgroundColor: 'rgba(0, 0, 0, 0.8)',
                                titleColor: '#ffffff',
                                bodyColor: '#ffffff',
                                borderColor: '#f57c00',
                                borderWidth: 1,
                                callbacks: {
                                    title: function(context) {
                                        return context[0].label;
                                    },
                                    label: function(context) {
                                        const count = context.parsed.y;
                                        return count === 1 ? '1 accusé de réception' : count + ' accusés de réception';
                                    }
                                }
                            }
                        },
                        scales: {
                            x: {
                                grid: {
                                    display: false
                                },
                                ticks: {
                                    maxRotation: 45,
                                    minRotation: 0
                                }
                            },
                            y: {
                                beginAtZero: true,
                                grid: {
                                    color: 'rgba(0, 0, 0, 0.1)'
                                },
                                ticks: {
                                    stepSize: 1,
                                    callback: function(value) {
                                        return Number.isInteger(value) ? value : '';
                                    }
                                }
                            }
                        },
                        elements: {
                            line: {
                                borderJoinStyle: 'round'
                            }
                        }
                    }
                });
            } catch (error) {
                console.error('Erreur lors de l\'initialisation du graphique:', error);
            }
        }

        function applyFilters() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const collaborateurFilter = document.getElementById('collaborateurFilter').value;
            const responsableFilter = document.getElementById('responsableFilter').value;
            const periodeFilter = document.getElementById('periodeFilter').value;

            const rows = document.querySelectorAll('#tableBody tr');
            let visibleCount = 0;

            rows.forEach(row => {
                const collaborateur = row.dataset.collaborateur.toLowerCase();
                const responsable = row.dataset.responsable.toLowerCase();
                const date = new Date(row.dataset.date);
                const remis = row.dataset.remis.toLowerCase();
                const recu = row.dataset.recu.toLowerCase();

                let show = true;

                // Recherche globale
                if (searchTerm && !collaborateur.includes(searchTerm) &&
                    !responsable.includes(searchTerm) && !remis.includes(searchTerm) &&
                    !recu.includes(searchTerm)) {
                    show = false;
                }

                // Filtre collaborateur
                if (collaborateurFilter && row.dataset.collaborateur !== collaborateurFilter) {
                    show = false;
                }

                // Filtre responsable
                if (responsableFilter && row.dataset.responsable !== responsableFilter) {
                    show = false;
                }

                // Filtre période
                if (periodeFilter) {
                    const now = new Date();
                    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

                    switch (periodeFilter) {
                        case 'today':
                            if (date < today || date >= new Date(today.getTime() + 24*60*60*1000)) {
                                show = false;
                            }
                            break;
                        case 'week':
                            const weekAgo = new Date(now.getTime() - 7*24*60*60*1000);
                            if (date < weekAgo) {
                                show = false;
                            }
                            break;
                        case 'month':
                            if (date.getMonth() !== now.getMonth() || date.getFullYear() !== now.getFullYear()) {
                                show = false;
                            }
                            break;
                        case 'year':
                            if (date.getFullYear() !== now.getFullYear()) {
                                show = false;
                            }
                            break;
                    }
                }

                if (show) {
                    row.style.display = '';
                    visibleCount++;
                } else {
                    row.style.display = 'none';
                }
            });

            document.getElementById('filteredCount').textContent = visibleCount;
        }

        function resetFilters() {
            document.getElementById('searchInput').value = '';
            document.getElementById('collaborateurFilter').value = '';
            document.getElementById('responsableFilter').value = '';
            document.getElementById('periodeFilter').value = '';
            applyFilters();
        }

        function sortTable(columnIndex) {
            const table = document.getElementById('historyTable');
            const tbody = table.querySelector('tbody');
            const rows = Array.from(tbody.querySelectorAll('tr'));

            rows.sort((a, b) => {
                const aText = a.cells[columnIndex].textContent.trim();
                const bText = b.cells[columnIndex].textContent.trim();

                if (columnIndex === 2) { // Date column
                    return new Date(a.dataset.date) - new Date(b.dataset.date);
                }

                return aText.localeCompare(bText);
            });

            rows.forEach(row => tbody.appendChild(row));
        }

        function viewDetails(id) {
            const data = allData.find(item => item.ID == id);
            if (!data) return;

            const modalContent = document.getElementById('modalContent');
            modalContent.innerHTML = `
                <div class="space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-user mr-2 text-schluter-blue"></i>Collaborateur
                            </label>
                            <div class="bg-gray-50 p-3 rounded-lg">${data.Collaborateur}</div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-user-tie mr-2 text-schluter-orange"></i>Responsable
                            </label>
                            <div class="bg-gray-50 p-3 rounded-lg">${data.Responsable}</div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-calendar mr-2 text-schluter-blue"></i>Date de création
                            </label>
                            <div class="bg-gray-50 p-3 rounded-lg">${new Date(data.Date).toLocaleDateString('fr-FR')} à ${new Date(data.Date).toLocaleTimeString('fr-FR')}</div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-hashtag mr-2 text-gray-500"></i>ID de l'accusé
                            </label>
                            <div class="bg-gray-50 p-3 rounded-lg">${data.ID}</div>
                        </div>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-hand-holding mr-2 text-green-600"></i>Matériel/Documents Remis
                        </label>
                        <div class="bg-green-50 p-4 rounded-lg border border-green-200">
                            ${data.Remis}
                        </div>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-inbox mr-2 text-blue-600"></i>Matériel/Documents Reçus
                        </label>
                        <div class="bg-blue-50 p-4 rounded-lg border border-blue-200">
                            ${data.Recu}
                        </div>
                    </div>

                    <div class="flex gap-3 pt-4">
                        <a href="accuse_reception.php?download_single_pdf=${data.ID}"
                           class="bg-schluter-blue hover:bg-schluter-blue-dark text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center gap-2">
                            <i class="fas fa-download"></i>
                            Télécharger le PDF
                        </a>
                        <button onclick="closeModal()"
                                class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                            Fermer
                        </button>
                    </div>
                </div>
            `;

            document.getElementById('detailsModal').classList.remove('hidden');
        }

        function closeModal() {
            document.getElementById('detailsModal').classList.add('hidden');
        }

        function exportToCSV() {
            try {
                // Utiliser les données visibles après filtrage
                const visibleRows = document.querySelectorAll('#tableBody tr:not([style*="display: none"])');
                const exportData = [];

                visibleRows.forEach(row => {
                    exportData.push({
                        Collaborateur: row.dataset.collaborateur,
                        Responsable: row.dataset.responsable,
                        Date: row.dataset.date,
                        Remis: row.dataset.remis,
                        Recu: row.dataset.recu
                    });
                });

                const headers = ['Collaborateur', 'Responsable', 'Date', 'Matériel Remis', 'Matériel Reçu'];
                const csvContent = [
                    headers.join(','),
                    ...exportData.map(row => [
                        `"${row.Collaborateur}"`,
                        `"${row.Responsable}"`,
                        `"${new Date(row.Date).toLocaleDateString('fr-FR')}"`,
                        `"${row.Remis.replace(/"/g, '""')}"`,
                        `"${row.Recu.replace(/"/g, '""')}"`
                    ].join(','))
                ].join('\n');

                const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
                const link = document.createElement('a');
                link.href = URL.createObjectURL(blob);
                link.download = `historique_accuses_reception_${new Date().toISOString().split('T')[0]}.csv`;
                link.click();

                // Notification de succès
                console.log(`Export CSV réussi: ${exportData.length} enregistrements exportés`);
            } catch (error) {
                console.error('Erreur lors de l\'export CSV:', error);
                alert('Erreur lors de l\'export CSV. Veuillez réessayer.');
            }
        }

        // Fonction de débogage pour vérifier les données
        function debugChartData() {
            console.log('=== DEBUG CHART DATA ===');
            console.log('monthlyLabels:', monthlyLabels);
            console.log('monthlyCounts:', monthlyCounts);
            console.log('allData length:', allData.length);
            console.log('Chart.js loaded:', typeof Chart !== 'undefined');
            console.log('Canvas element:', document.getElementById('monthlyChart'));
        }

        // Appeler le debug en cas de problème (décommentez la ligne suivante)
        // debugChartData();

        function showChartError() {
            const chartContainer = document.getElementById('monthlyChart').parentElement;
            chartContainer.innerHTML = `
                <div class="text-center text-gray-500 py-8">
                    <i class="fas fa-exclamation-triangle text-4xl mb-4 text-yellow-500"></i>
                    <p class="text-lg font-medium">Erreur de chargement du graphique</p>
                    <p class="text-sm">Chart.js n'a pas pu être chargé. Veuillez rafraîchir la page.</p>
                    <button onclick="location.reload()" class="mt-4 bg-schluter-blue text-white px-4 py-2 rounded-lg hover:bg-schluter-blue-dark transition-colors">
                        <i class="fas fa-refresh mr-2"></i>Rafraîchir
                    </button>
                </div>
            `;
        }

        // Fermer la modal en cliquant à l'extérieur
        document.getElementById('detailsModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });
    </script>

    <?php include 'bottom_bar.php'; ?>
</body>
</html>