<?php
/**
 * Script de test pour le système d'évaluation des prestataires
 * Ce script teste les fonctionnalités principales du système
 */

require_once __DIR__ . '/config/database.php';

echo "<h1>Test du Système d'Évaluation des Prestataires</h1>";

try {
    // Test 1: Vérifier la structure des tables
    echo "<h2>Test 1: Vérification de la structure des tables</h2>";

    $tables = ['evaluations', 'evaluation_prestataires', 'evaluation_services'];
    foreach ($tables as $table) {
        $stmt = $db->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            echo "✅ Table '$table' existe<br>";
        } else {
            echo "❌ Table '$table' n'existe pas<br>";
        }
    }

    // Test 2: Vérifier les données par défaut
    echo "<h2>Test 2: Vérification des données par défaut</h2>";

    $stmt = $db->query("SELECT COUNT(*) as count FROM evaluation_prestataires");
    $providerCount = $stmt->fetch()['count'];
    echo "Nombre de prestataires: $providerCount<br>";

    $stmt = $db->query("SELECT COUNT(*) as count FROM evaluation_services");
    $serviceCount = $stmt->fetch()['count'];
    echo "Nombre de services: $serviceCount<br>";

    $stmt = $db->query("SELECT COUNT(*) as count FROM evaluations");
    $evaluationCount = $stmt->fetch()['count'];
    echo "Nombre d'évaluations: $evaluationCount<br>";

    // Test 3: Insérer une évaluation de test
    echo "<h2>Test 3: Insertion d'une évaluation de test</h2>";

    $testData = [
        'Test Prestataire',
        'Test Service',
        4, // qualité
        5, // délai
        3, // communication
        'Commentaire de test'
    ];

    $stmt = $db->prepare("INSERT INTO evaluations (prestataire, service, qualite, delai, communication, commentaires) VALUES (?, ?, ?, ?, ?, ?)");
    if ($stmt->execute($testData)) {
        echo "✅ Évaluation de test insérée avec succès<br>";
        $testId = $db->lastInsertId();
        echo "ID de l'évaluation: $testId<br>";
    } else {
        echo "❌ Erreur lors de l'insertion de l'évaluation de test<br>";
    }

    // Test 4: Calculer les moyennes
    echo "<h2>Test 4: Calcul des moyennes</h2>";

    $stmt = $db->query("
        SELECT
            prestataire,
            ROUND(AVG(qualite), 2) as avg_qualite,
            ROUND(AVG(delai), 2) as avg_delai,
            ROUND(AVG(communication), 2) as avg_communication,
            ROUND(AVG((qualite + delai + communication) / 3), 2) as avg_globale,
            COUNT(*) as nb_evaluations
        FROM evaluations
        GROUP BY prestataire
        ORDER BY avg_globale DESC
    ");

    $averages = $stmt->fetchAll(PDO::FETCH_ASSOC);

    if (count($averages) > 0) {
        echo "✅ Calcul des moyennes réussi<br>";
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>Prestataire</th><th>Qualité</th><th>Délai</th><th>Communication</th><th>Global</th><th>Nb Éval</th></tr>";
        foreach ($averages as $avg) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($avg['prestataire']) . "</td>";
            echo "<td>" . $avg['avg_qualite'] . "</td>";
            echo "<td>" . $avg['avg_delai'] . "</td>";
            echo "<td>" . $avg['avg_communication'] . "</td>";
            echo "<td><strong>" . $avg['avg_globale'] . "</strong></td>";
            echo "<td>" . $avg['nb_evaluations'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "❌ Aucune moyenne calculée<br>";
    }

    // Test 5: Nettoyer les données de test
    echo "<h2>Test 5: Nettoyage des données de test</h2>";

    if (isset($testId)) {
        $stmt = $db->prepare("DELETE FROM evaluations WHERE id = ?");
        if ($stmt->execute([$testId])) {
            echo "✅ Évaluation de test supprimée<br>";
        } else {
            echo "❌ Erreur lors de la suppression de l'évaluation de test<br>";
        }
    }

    // Test 6: Vérifier les liens entre les pages
    echo "<h2>Test 6: Vérification des fichiers du système</h2>";

    $files = [
        'evaluation.php' => 'Formulaire d\'évaluation',
        'historique_evaluations.php' => 'Historique des évaluations',
        'moyenne_satisfaction.php' => 'Moyennes de satisfaction',
        'config/database.php' => 'Configuration de la base de données'
    ];

    foreach ($files as $file => $description) {
        if (file_exists($file)) {
            echo "✅ $description ($file) existe<br>";
        } else {
            echo "❌ $description ($file) n'existe pas<br>";
        }
    }

    echo "<h2>✅ Tests terminés avec succès!</h2>";
    echo "<p><strong>Le système d'évaluation des prestataires est opérationnel.</strong></p>";

    echo "<h3>Liens vers les pages:</h3>";
    echo "<ul>";
    echo "<li><a href='evaluation.php'>Créer une nouvelle évaluation</a></li>";
    echo "<li><a href='historique_evaluations.php'>Voir l'historique des évaluations</a></li>";
    echo "<li><a href='moyenne_satisfaction.php'>Voir les moyennes de satisfaction</a></li>";
    echo "</ul>";

} catch (Exception $e) {
    echo "<h2>❌ Erreur lors des tests</h2>";
    echo "<p>Erreur: " . $e->getMessage() . "</p>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 800px;
    margin: 20px auto;
    padding: 20px;
    background-color: #f4f4f4;
}

h1, h2 {
    color: #f57c00;
}

table {
    background-color: white;
    width: 100%;
}

th {
    background-color: #f57c00;
    color: white;
    padding: 8px;
}

td {
    padding: 8px;
    border: 1px solid #ddd;
}

a {
    color: #f57c00;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}
</style>
