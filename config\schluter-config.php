<?php
/**
 * Configuration centralisée pour le portail Schluter Systems
 * Contient les constantes, configurations et utilitaires partagés
 */

// Configuration des couleurs Schluter
define('SCHLUTER_COLORS', [
    'orange' => '#f57c00',
    'orange-dark' => '#e65100',
    'orange-light' => '#ff9800',
    'blue' => '#005ea2',
    'blue-dark' => '#004c87',
    'blue-light' => '#1976d2',
    'gray' => '#333333',
    'gray-light' => '#f4f4f4',
    'gray-medium' => '#666666',
    'success' => '#28a745',
    'warning' => '#ffc107',
    'danger' => '#dc3545',
    'info' => '#17a2b8'
]);

// Configuration Tailwind centralisée pour injection dans les pages
function getSchlutterTailwindConfig() {
    return "
    tailwind.config = {
        theme: {
            extend: {
                colors: {
                    schluter: {
                        orange: '#f57c00',
                        'orange-dark': '#e65100',
                        'orange-light': '#ff9800',
                        blue: '#005ea2',
                        'blue-dark': '#004c87',
                        'blue-light': '#1976d2',
                        gray: '#333333',
                        'gray-light': '#f4f4f4',
                        'gray-medium': '#666666',
                        'gray-dark': '#1a1a1a',
                        success: '#28a745',
                        warning: '#ffc107',
                        danger: '#dc3545',
                        info: '#17a2b8',
                        background: '#f8f9fa',
                        'background-dark': '#e9ecef',
                        surface: '#ffffff',
                        'surface-dark': '#f1f3f4'
                    }
                },
                fontFamily: {
                    'sans': ['Inter', 'system-ui', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'sans-serif'],
                    'heading': ['Inter', 'system-ui', 'sans-serif']
                },
                boxShadow: {
                    'schluter': '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
                    'schluter-lg': '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
                    'schluter-xl': '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)'
                },
                animation: {
                    'fade-in': 'fadeIn 0.5s ease-in-out',
                    'slide-up': 'slideUp 0.3s ease-out',
                    'scale-in': 'scaleIn 0.2s ease-out',
                    'bounce-subtle': 'bounceSubtle 0.6s ease-in-out'
                },
                keyframes: {
                    fadeIn: {
                        '0%': { opacity: '0' },
                        '100%': { opacity: '1' }
                    },
                    slideUp: {
                        '0%': { transform: 'translateY(10px)', opacity: '0' },
                        '100%': { transform: 'translateY(0)', opacity: '1' }
                    },
                    scaleIn: {
                        '0%': { transform: 'scale(0.95)', opacity: '0' },
                        '100%': { transform: 'scale(1)', opacity: '1' }
                    },
                    bounceSubtle: {
                        '0%, 100%': { transform: 'translateY(0)' },
                        '50%': { transform: 'translateY(-5px)' }
                    }
                }
            }
        }
    }";
}

// Configuration des CDN et ressources externes
function getSchlutterAssets() {
    return [
        'tailwind_cdn' => 'https://cdn.tailwindcss.com',
        'fontawesome_cdn' => 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css',
        'gsap_cdn' => 'https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js',
        'inter_font' => 'https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap'
    ];
}

// Fonction pour générer le head HTML standardisé
function generateSchlutterHead($title = 'Schluter Systems', $description = '', $additionalAssets = []) {
    $assets = getSchlutterAssets();
    $tailwindConfig = getSchlutterTailwindConfig();

    $head = "
    <meta charset=\"UTF-8\">
    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">
    <meta name=\"description\" content=\"{$description}\">
    <title>{$title}</title>

    <!-- Fonts -->
    <link rel=\"preconnect\" href=\"https://fonts.googleapis.com\">
    <link rel=\"preconnect\" href=\"https://fonts.gstatic.com\" crossorigin>
    <link href=\"{$assets['inter_font']}\" rel=\"stylesheet\">

    <!-- CSS Libraries -->
    <script src=\"{$assets['tailwind_cdn']}\"></script>
    <link rel=\"stylesheet\" href=\"{$assets['fontawesome_cdn']}\">

    <!-- Tailwind Configuration -->
    <script>
    {$tailwindConfig}
    </script>

    <!-- Custom Styles -->
    <style>
        body {
            font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        /* Animations personnalisées */
        .animate-fade-in {
            animation: fadeIn 0.5s ease-in-out;
        }

        .animate-slide-up {
            animation: slideUp 0.3s ease-out;
        }

        .animate-scale-in {
            animation: scaleIn 0.2s ease-out;
        }

        /* Utilitaires personnalisés */
        .glass-effect {
            background: rgba(255, 255, 255, 0.25);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.18);
        }

        .gradient-schluter {
            background: linear-gradient(135deg, #005ea2 0%, #f57c00 100%);
        }

        .gradient-schluter-reverse {
            background: linear-gradient(135deg, #f57c00 0%, #005ea2 100%);
        }

        /* Focus states améliorés */
        .focus-schluter:focus {
            outline: 2px solid #f57c00;
            outline-offset: 2px;
        }

        /* Hover effects */
        .hover-lift:hover {
            transform: translateY(-2px);
            transition: transform 0.2s ease-out;
        }

        .hover-scale:hover {
            transform: scale(1.02);
            transition: transform 0.2s ease-out;
        }
    </style>";

    // Ajouter les assets supplémentaires
    foreach ($additionalAssets as $asset) {
        if (strpos($asset, '.css') !== false) {
            $head .= "\n    <link rel=\"stylesheet\" href=\"{$asset}\">";
        } elseif (strpos($asset, '.js') !== false) {
            $head .= "\n    <script src=\"{$asset}\"></script>";
        }
    }

    return $head;
}

// Classes CSS utilitaires pour les composants
define('SCHLUTER_COMPONENT_CLASSES', [
    'card' => 'bg-white rounded-xl shadow-schluter p-6 hover-lift transition-all duration-300',
    'card_header' => 'bg-gradient-to-r from-schluter-blue to-schluter-orange rounded-lg shadow-lg p-8 mb-8 text-white',
    'button_primary' => 'bg-schluter-orange hover:bg-schluter-orange-dark text-white font-semibold py-3 px-6 rounded-lg transition-all duration-300 hover-scale focus-schluter',
    'button_secondary' => 'bg-schluter-blue hover:bg-schluter-blue-dark text-white font-semibold py-3 px-6 rounded-lg transition-all duration-300 hover-scale focus-schluter',
    'input' => 'w-full px-4 py-3 border-2 border-gray-200 rounded-lg focus:ring-2 focus:ring-schluter-orange focus:border-schluter-orange transition duration-200 focus-schluter',
    'table' => 'min-w-full bg-white rounded-lg overflow-hidden shadow-schluter',
    'table_header' => 'bg-gradient-to-r from-schluter-blue to-schluter-orange text-white',
    'badge_success' => 'inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-schluter-success text-white',
    'badge_warning' => 'inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-schluter-warning text-white',
    'badge_danger' => 'inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-schluter-danger text-white'
]);

// Fonction pour obtenir les classes d'un composant
function getComponentClasses($component) {
    return SCHLUTER_COMPONENT_CLASSES[$component] ?? '';
}

// Configuration des modules et navigation
define('SCHLUTER_MODULES', [
    'main' => [
        'name' => 'Accueil',
        'url' => 'index.php',
        'icon' => 'fas fa-home'
    ],
    'it' => [
        'name' => 'Informatique',
        'url' => 'IT.php',
        'icon' => 'fas fa-laptop',
        'submodules' => [
            'inventory' => ['name' => 'Inventaire', 'url' => 'inventaire.php', 'icon' => 'fas fa-boxes'],
            'receipts' => ['name' => 'Accusés de Réception', 'url' => 'accuse_reception.php', 'icon' => 'fas fa-file-signature'],
            'eol' => ['name' => 'Changements de Poste', 'url' => 'End_Of_Life.php', 'icon' => 'fas fa-exchange-alt']
        ]
    ],
    'rse' => [
        'name' => 'RSE',
        'url' => 'RSE.php',
        'icon' => 'fas fa-leaf',
        'submodules' => [
            'evaluation' => ['name' => 'Évaluation des Prestataires', 'url' => 'evaluation.php', 'icon' => 'fas fa-star'],
            'community' => ['name' => 'Engagement Communautaire', 'url' => 'engagement_communautaire.php', 'icon' => 'fas fa-hands-helping'],
            'reports' => ['name' => 'Rapports RSE', 'url' => 'rapports_rse.php', 'icon' => 'fas fa-chart-pie']
        ]
    ],
    'adv' => [
        'name' => 'ADV',
        'url' => 'ADV.php',
        'icon' => 'fas fa-tools'
    ],
    'assistance' => [
        'name' => 'Assistance',
        'url' => 'assistance.php',
        'icon' => 'fas fa-life-ring'
    ],
    'dashboard' => [
        'name' => 'Tableau de Bord',
        'url' => 'dashboard.php',
        'icon' => 'fas fa-tachometer-alt'
    ],
    'events' => [
        'name' => 'Événements',
        'url' => 'events.php',
        'icon' => 'fas fa-calendar-alt'
    ]
]);
?>
