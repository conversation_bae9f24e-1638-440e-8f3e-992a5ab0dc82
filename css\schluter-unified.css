/**
 * Styles unifiés pour le portail Schluter Systems
 * Remplace les multiples configurations Tailwind et CSS personnalisés
 */

/* Import de la police Inter */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* Variables CSS pour les couleurs Schluter */
:root {
  /* Couleurs principales */
  --schluter-orange: #f57c00;
  --schluter-orange-dark: #e65100;
  --schluter-orange-light: #ff9800;
  --schluter-blue: #005ea2;
  --schluter-blue-dark: #004c87;
  --schluter-blue-light: #1976d2;
  
  /* Couleurs neutres */
  --schluter-gray: #333333;
  --schluter-gray-light: #f4f4f4;
  --schluter-gray-medium: #666666;
  --schluter-gray-dark: #1a1a1a;
  
  /* Couleurs système */
  --schluter-success: #28a745;
  --schluter-warning: #ffc107;
  --schluter-danger: #dc3545;
  --schluter-info: #17a2b8;
  
  /* Couleurs de fond */
  --schluter-background: #f8f9fa;
  --schluter-background-dark: #e9ecef;
  --schluter-surface: #ffffff;
  --schluter-surface-dark: #f1f3f4;
  
  /* Ombres */
  --shadow-schluter: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-schluter-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-schluter-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  
  /* Transitions */
  --transition-fast: 0.15s ease-out;
  --transition-normal: 0.3s ease-out;
  --transition-slow: 0.5s ease-out;
  
  /* Rayons de bordure */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
}

/* Reset et styles de base */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
  color: var(--schluter-gray);
  background-color: var(--schluter-background);
  font-size: 16px;
}

/* Typographie */
h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  line-height: 1.2;
  margin-bottom: 1rem;
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.5rem; }
h4 { font-size: 1.25rem; }
h5 { font-size: 1.125rem; }
h6 { font-size: 1rem; }

p {
  margin-bottom: 1rem;
}

/* Liens */
a {
  color: var(--schluter-orange);
  text-decoration: none;
  transition: color var(--transition-fast);
}

a:hover {
  color: var(--schluter-orange-dark);
}

/* Boutons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  border-radius: var(--radius-lg);
  border: none;
  cursor: pointer;
  transition: all var(--transition-fast);
  text-decoration: none;
  font-size: 0.875rem;
  line-height: 1;
  gap: 0.5rem;
}

.btn:focus {
  outline: 2px solid var(--schluter-orange);
  outline-offset: 2px;
}

.btn-primary {
  background-color: var(--schluter-orange);
  color: white;
}

.btn-primary:hover {
  background-color: var(--schluter-orange-dark);
  transform: translateY(-1px);
  box-shadow: var(--shadow-schluter-lg);
}

.btn-secondary {
  background-color: var(--schluter-blue);
  color: white;
}

.btn-secondary:hover {
  background-color: var(--schluter-blue-dark);
  transform: translateY(-1px);
  box-shadow: var(--shadow-schluter-lg);
}

.btn-outline {
  background-color: transparent;
  color: var(--schluter-orange);
  border: 2px solid var(--schluter-orange);
}

.btn-outline:hover {
  background-color: var(--schluter-orange);
  color: white;
}

.btn-danger {
  background-color: var(--schluter-danger);
  color: white;
}

.btn-danger:hover {
  background-color: #c82333;
  transform: translateY(-1px);
}

/* Cartes */
.card {
  background-color: var(--schluter-surface);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-schluter);
  padding: 1.5rem;
  transition: all var(--transition-normal);
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-schluter-lg);
}

.card-header {
  background: linear-gradient(135deg, var(--schluter-blue) 0%, var(--schluter-orange) 100%);
  color: white;
  padding: 2rem;
  border-radius: var(--radius-lg);
  margin-bottom: 2rem;
  box-shadow: var(--shadow-schluter-lg);
}

/* Formulaires */
.form-group {
  margin-bottom: 1.5rem;
}

.form-label {
  display: block;
  font-weight: 600;
  color: var(--schluter-gray);
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
}

.form-input {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 2px solid #e5e7eb;
  border-radius: var(--radius-lg);
  font-size: 1rem;
  transition: all var(--transition-fast);
  background-color: var(--schluter-surface);
}

.form-input:focus {
  outline: none;
  border-color: var(--schluter-orange);
  box-shadow: 0 0 0 3px rgba(245, 124, 0, 0.1);
}

.form-input:invalid {
  border-color: var(--schluter-danger);
}

.form-textarea {
  resize: vertical;
  min-height: 100px;
}

.form-select {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 2.5rem;
}

/* Tableaux */
.table-container {
  overflow-x: auto;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-schluter);
}

.table {
  width: 100%;
  background-color: var(--schluter-surface);
  border-collapse: collapse;
}

.table th {
  background: linear-gradient(135deg, var(--schluter-blue) 0%, var(--schluter-orange) 100%);
  color: white;
  padding: 1rem 1.5rem;
  text-align: left;
  font-weight: 600;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.table td {
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #e5e7eb;
  vertical-align: top;
}

.table tbody tr:hover {
  background-color: var(--schluter-background);
}

/* Badges */
.badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.badge-success {
  background-color: var(--schluter-success);
  color: white;
}

.badge-warning {
  background-color: var(--schluter-warning);
  color: white;
}

.badge-danger {
  background-color: var(--schluter-danger);
  color: white;
}

.badge-info {
  background-color: var(--schluter-info);
  color: white;
}

/* Alertes */
.alert {
  padding: 1rem 1.5rem;
  border-radius: var(--radius-lg);
  border-left: 4px solid;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.alert-success {
  background-color: #d4edda;
  border-color: var(--schluter-success);
  color: #155724;
}

.alert-warning {
  background-color: #fff3cd;
  border-color: var(--schluter-warning);
  color: #856404;
}

.alert-danger {
  background-color: #f8d7da;
  border-color: var(--schluter-danger);
  color: #721c24;
}

.alert-info {
  background-color: #d1ecf1;
  border-color: var(--schluter-info);
  color: #0c5460;
}

/* Navigation */
.navbar {
  background-color: var(--schluter-surface);
  box-shadow: var(--shadow-schluter-lg);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
}

.navbar-container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 1rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 4rem;
}

.navbar-logo {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-weight: 700;
  font-size: 1.25rem;
  color: var(--schluter-gray);
  text-decoration: none;
}

.navbar-logo img {
  height: 2.5rem;
  width: auto;
}

.navbar-menu {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  list-style: none;
}

.navbar-link {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: var(--radius-lg);
  color: var(--schluter-gray);
  text-decoration: none;
  font-weight: 500;
  transition: all var(--transition-fast);
}

.navbar-link:hover {
  background-color: var(--schluter-orange);
  color: white;
  transform: translateY(-1px);
}

.navbar-link.active {
  background-color: var(--schluter-orange);
  color: white;
}

/* Utilitaires */
.gradient-schluter {
  background: linear-gradient(135deg, var(--schluter-blue) 0%, var(--schluter-orange) 100%);
}

.gradient-schluter-reverse {
  background: linear-gradient(135deg, var(--schluter-orange) 0%, var(--schluter-blue) 100%);
}

.glass-effect {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.18);
}

.hover-lift:hover {
  transform: translateY(-2px);
}

.hover-scale:hover {
  transform: scale(1.02);
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { 
    transform: translateY(20px); 
    opacity: 0; 
  }
  to { 
    transform: translateY(0); 
    opacity: 1; 
  }
}

@keyframes scaleIn {
  from { 
    transform: scale(0.95); 
    opacity: 0; 
  }
  to { 
    transform: scale(1); 
    opacity: 1; 
  }
}

.animate-fade-in {
  animation: fadeIn 0.5s ease-out;
}

.animate-slide-up {
  animation: slideUp 0.3s ease-out;
}

.animate-scale-in {
  animation: scaleIn 0.2s ease-out;
}

/* Responsive Design */
@media (max-width: 768px) {
  .navbar-menu {
    display: none;
  }
  
  .card {
    padding: 1rem;
  }
  
  .card-header {
    padding: 1.5rem;
  }
  
  h1 { font-size: 2rem; }
  h2 { font-size: 1.75rem; }
  h3 { font-size: 1.25rem; }
  
  .table th,
  .table td {
    padding: 0.75rem;
  }
}

@media (max-width: 480px) {
  .navbar-container {
    padding: 0 0.5rem;
  }
  
  .btn {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
  }
  
  .form-input {
    padding: 0.5rem 0.75rem;
  }
}
