<?php
/**
 * Script simple d'initialisation de la base de données
 */

// Configuration directe de la base de données
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'schluter_db_dev';

try {
    echo "<h2>Initialisation de la base de données</h2>";
    
    // Connexion à MySQL
    $pdo = new PDO("mysql:host=$host", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Créer la base de données
    $pdo->exec("CREATE DATABASE IF NOT EXISTS `$database` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "✅ Base de données créée<br>";
    
    // Sélectionner la base de données
    $pdo->exec("USE `$database`");
    
    // Créer la table services directement
    $pdo->exec("CREATE TABLE IF NOT EXISTS services (
        id INT AUTO_INCREMENT PRIMARY KEY,
        title VARCHAR(100) NOT NULL,
        description TEXT,
        icon VARCHAR(50),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )");
    echo "✅ Table services créée<br>";
    
    // Créer la table users
    $pdo->exec("CREATE TABLE IF NOT EXISTS users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) NOT NULL UNIQUE,
        password VARCHAR(255) NOT NULL,
        email VARCHAR(100),
        role VARCHAR(20) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )");
    echo "✅ Table users créée<br>";
    
    // Insérer des services par défaut
    $pdo->exec("INSERT IGNORE INTO services (title, description, icon) VALUES
        ('Informatique', 'Gestion des ressources informatiques, inventaire et support technique', 'fas fa-laptop'),
        ('RSE', 'Responsabilité Sociétale des Entreprises et évaluation des prestataires', 'fas fa-leaf'),
        ('ADV', 'Service Après-Vente et support client', 'fas fa-tools'),
        ('Assistance', 'Support technique et demandes d\'aide', 'fas fa-life-ring'),
        ('Tableau de Bord', 'Statistiques et indicateurs de performance', 'fas fa-tachometer-alt'),
        ('Événements', 'Calendrier et gestion des événements', 'fas fa-calendar-alt')
    ");
    echo "✅ Services par défaut ajoutés<br>";
    
    // Vérifier le contenu
    $count = $pdo->query("SELECT COUNT(*) FROM services")->fetchColumn();
    echo "ℹ️ Nombre de services: $count<br>";
    
    echo "<br><strong>✅ Initialisation terminée!</strong><br>";
    echo "<a href='index.php'>Tester l'application</a>";
    
} catch (PDOException $e) {
    echo "❌ Erreur: " . $e->getMessage();
}
?>
