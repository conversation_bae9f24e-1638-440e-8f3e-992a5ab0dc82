<?php
session_start();
if (!isset($_SESSION['logged_in']) || !$_SESSION['logged_in']) {
    header('Location: login.php');
    exit();
}

// Inclusion des configurations et composants
require_once 'config/database.php';
require_once 'config/schluter-config.php';
require_once 'templates/components.php';

// Charger les services depuis la base de données
function loadServices() {
    $db = getDB();
    $services = $db->fetchAll('SELECT title, description, icon FROM services');

    // Services par défaut si la table est vide
    if (empty($services)) {
        return [
            ['Informatique', 'Gestion des ressources informatiques, inventaire et support technique', 'fas fa-laptop'],
            ['RSE', 'Responsabilité Sociétale des Entreprises et évaluation des prestataires', 'fas fa-leaf'],
            ['ADV', 'Service Après-Vente et support client', 'fas fa-tools'],
            ['Assistance', 'Support technique et demandes d\'aide', 'fas fa-life-ring'],
            ['Tableau de Bord', 'Statistiques et indicateurs de performance', 'fas fa-tachometer-alt'],
            ['Événements', 'Calendrier et gestion des événements', 'fas fa-calendar-alt']
        ];
    }

    return $services;
}

// Sauvegarder un nouveau service dans la base de données
function saveService($title, $description, $icon) {
    global $db;
    $stmt = $db->prepare('INSERT INTO services (title, description, icon) VALUES (?, ?, ?)');
    $stmt->execute([$title, $description, $icon]);
}

// Supprimer un service de la base de données
function deleteService($title) {
    global $db;
    $stmt = $db->prepare('DELETE FROM services WHERE title = ?');
    $stmt->execute([$title]);
}

// Charger les services pour l'affichage
$services = loadServices();

// Gérer la soumission du formulaire pour ajouter ou supprimer un service
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action']) && $_POST['action'] === 'add') {
        $title = htmlspecialchars($_POST['title']);
        $description = htmlspecialchars($_POST['description']);
        $icon = htmlspecialchars($_POST['icon']);
        saveService($title, $description, $icon);
    } elseif (isset($_POST['action']) && $_POST['action'] === 'delete') {
        $titleToDelete = htmlspecialchars($_POST['title']);
        deleteService($titleToDelete);
    }
    header("Location: index.php");
    exit();
}
// Charger les services
$services = loadServices();
?>

<?php
renderSchlutterHeader(
    'Portail Intranet - Schluter Systems',
    'Portail intranet de Schluter Systems pour accéder aux ressources internes.',
    ['css/schluter-unified.css', 'js/schluter-common.js']
);

// Afficher la navigation
renderSchlutterNavigation($_SERVER['PHP_SELF']);

// Afficher la bannière principale
renderSchlutterBanner(
    'Bienvenue sur le Portail Intranet',
    'Votre espace dédié pour accéder à toutes les ressources internes de Schluter Systems'
);
?>

<!-- Section À propos -->
<div class="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
    <div class="card animate-on-scroll" data-animate="slide-up">
        <div class="text-center">
            <div class="bg-gradient-to-br from-schluter-blue to-schluter-orange text-white p-4 rounded-xl mb-6 inline-block">
                <i class="fas fa-building text-3xl"></i>
            </div>
            <h2 class="text-3xl font-bold text-schluter-gray mb-4">À propos de Schluter Systems</h2>
            <p class="text-schluter-gray-medium text-lg leading-relaxed">
                Schluter Systems est une entreprise innovante spécialisée dans les solutions pour la construction et la rénovation.
                Nous nous engageons à fournir des produits de haute qualité et un service exceptionnel à nos clients.
            </p>
        </div>
    </div>
</div>

<!-- Section de recherche -->
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mb-12">
    <div class="card animate-on-scroll" data-animate="slide-up">
        <div class="flex items-center mb-6">
            <div class="bg-schluter-orange text-white p-3 rounded-lg mr-4">
                <i class="fas fa-search text-xl"></i>
            </div>
            <h2 class="text-2xl font-semibold text-schluter-gray">Rechercher un Service</h2>
        </div>
        <div class="relative">
            <input type="text"
                   id="searchServices"
                   placeholder="Tapez le nom ou la description d'un service..."
                   class="form-input pr-12"
                   data-search=".service-card"
                   aria-label="Rechercher un service">
            <i class="fas fa-search absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
        </div>
    </div>
</div>

<!-- Grille des services -->
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mb-16">
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        <?php foreach ($services as $index => $service):
            $url = strtolower($service[0]) . '.php';
            // Mapping des URLs spéciales
            $urlMapping = [
                'informatique.php' => 'IT.php',
                'rse.php' => 'RSE.php',
                'adv.php' => 'ADV.php'
            ];
            if (isset($urlMapping[$url])) {
                $url = $urlMapping[$url];
            }
        ?>
            <div class="service-card animate-on-scroll" data-animate="scale-in" style="animation-delay: <?= $index * 0.1 ?>s;">
                <?php
                renderSchlutterServiceCard(
                    $service[0],
                    $service[1],
                    $url,
                    $service[2]
                );
                ?>
            </div>
        <?php endforeach; ?>
    </div>
</div>

<!-- Section statistiques rapides -->
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mb-16">
    <div class="card-header animate-on-scroll" data-animate="slide-up">
        <div class="text-center">
            <h2 class="text-3xl font-bold mb-4">Statistiques du Portail</h2>
            <p class="text-xl opacity-90">Aperçu de l'activité récente</p>
        </div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mt-8">
        <div class="card text-center animate-on-scroll" data-animate="scale-in">
            <div class="bg-schluter-success text-white p-4 rounded-xl mb-4 inline-block">
                <i class="fas fa-users text-2xl"></i>
            </div>
            <h3 class="text-2xl font-bold text-schluter-gray mb-2">
                <?= isset($_SESSION['user_count']) ? $_SESSION['user_count'] : '25+' ?>
            </h3>
            <p class="text-schluter-gray-medium">Utilisateurs Actifs</p>
        </div>

        <div class="card text-center animate-on-scroll" data-animate="scale-in" style="animation-delay: 0.1s;">
            <div class="bg-schluter-info text-white p-4 rounded-xl mb-4 inline-block">
                <i class="fas fa-tasks text-2xl"></i>
            </div>
            <h3 class="text-2xl font-bold text-schluter-gray mb-2">
                <?= count($services) ?>
            </h3>
            <p class="text-schluter-gray-medium">Services Disponibles</p>
        </div>

        <div class="card text-center animate-on-scroll" data-animate="scale-in" style="animation-delay: 0.2s;">
            <div class="bg-schluter-warning text-white p-4 rounded-xl mb-4 inline-block">
                <i class="fas fa-clock text-2xl"></i>
            </div>
            <h3 class="text-2xl font-bold text-schluter-gray mb-2">24/7</h3>
            <p class="text-schluter-gray-medium">Support Disponible</p>
        </div>
    </div>
</div>

<?php renderSchlutterFooter(); ?>
