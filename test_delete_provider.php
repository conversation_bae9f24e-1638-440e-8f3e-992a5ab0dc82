<?php
/**
 * Script de test pour la suppression de prestataires
 */

require_once __DIR__ . '/config/database.php';

echo "<h1>Test de Suppression de Prestataires</h1>";

try {
    // Ajouter un prestataire de test
    echo "<h2>Étape 1: Ajout d'un prestataire de test</h2>";
    $testProvider = "Test Prestataire " . date('Y-m-d H:i:s');
    
    $stmt = $db->prepare("INSERT INTO evaluation_prestataires (nom) VALUES (?)");
    if ($stmt->execute([$testProvider])) {
        echo "✅ Prestataire de test ajouté: $testProvider<br>";
    } else {
        echo "❌ Erreur lors de l'ajout du prestataire de test<br>";
        exit;
    }

    // Vérifier que le prestataire existe
    echo "<h2>Étape 2: Vérification de l'existence</h2>";
    $stmt = $db->prepare("SELECT COUNT(*) as count FROM evaluation_prestataires WHERE nom = ?");
    $stmt->execute([$testProvider]);
    $count = $stmt->fetch()['count'];
    
    if ($count > 0) {
        echo "✅ Prestataire trouvé dans la base de données<br>";
    } else {
        echo "❌ Prestataire non trouvé<br>";
        exit;
    }

    // Tester la suppression
    echo "<h2>Étape 3: Test de suppression</h2>";
    
    // Simuler la fonction handleDb pour removeProvider
    $db->beginTransaction();
    try {
        // Supprimer les évaluations associées (s'il y en a)
        $stmt = $db->prepare("DELETE FROM evaluations WHERE prestataire = ?");
        $stmt->execute([$testProvider]);
        echo "✅ Évaluations associées supprimées<br>";
        
        // Supprimer le prestataire
        $stmt = $db->prepare("DELETE FROM evaluation_prestataires WHERE nom = ?");
        $result = $stmt->execute([$testProvider]);
        
        if ($result) {
            echo "✅ Prestataire supprimé avec succès<br>";
        } else {
            echo "❌ Erreur lors de la suppression<br>";
        }
        
        $db->commit();
        echo "✅ Transaction validée<br>";
        
    } catch (Exception $e) {
        $db->rollback();
        echo "❌ Erreur lors de la transaction: " . $e->getMessage() . "<br>";
        throw $e;
    }

    // Vérifier que le prestataire a été supprimé
    echo "<h2>Étape 4: Vérification de la suppression</h2>";
    $stmt = $db->prepare("SELECT COUNT(*) as count FROM evaluation_prestataires WHERE nom = ?");
    $stmt->execute([$testProvider]);
    $count = $stmt->fetch()['count'];
    
    if ($count == 0) {
        echo "✅ Prestataire correctement supprimé de la base de données<br>";
    } else {
        echo "❌ Le prestataire existe encore dans la base de données<br>";
    }

    // Test de la réponse JSON
    echo "<h2>Étape 5: Test de la réponse JSON</h2>";
    
    $response = ['status' => 'success', 'message' => 'Prestataire supprimé avec succès!'];
    $jsonResponse = json_encode($response);
    
    if ($jsonResponse !== false) {
        echo "✅ Réponse JSON valide: $jsonResponse<br>";
    } else {
        echo "❌ Erreur lors de l'encodage JSON<br>";
    }

    // Lister tous les prestataires actuels
    echo "<h2>Étape 6: Liste des prestataires actuels</h2>";
    $stmt = $db->query("SELECT nom FROM evaluation_prestataires ORDER BY nom");
    $providers = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    if (count($providers) > 0) {
        echo "Prestataires disponibles:<br>";
        foreach ($providers as $provider) {
            echo "- " . htmlspecialchars($provider) . "<br>";
        }
    } else {
        echo "Aucun prestataire dans la base de données<br>";
    }

    echo "<h2>🎉 Test terminé avec succès!</h2>";
    echo "<p>La suppression de prestataires fonctionne correctement.</p>";

} catch (Exception $e) {
    echo "<h2>❌ Erreur lors du test</h2>";
    echo "<p>Erreur: " . $e->getMessage() . "</p>";
    echo "<p>Trace: " . $e->getTraceAsString() . "</p>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 800px;
    margin: 20px auto;
    padding: 20px;
    background-color: #f4f4f4;
}

h1, h2 {
    color: #f57c00;
}

p {
    background-color: white;
    padding: 10px;
    border-radius: 4px;
    border-left: 4px solid #f57c00;
}
</style>
