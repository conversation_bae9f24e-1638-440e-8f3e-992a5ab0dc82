<?php
require_once __DIR__ . '/config/database.php';

try {
    // R<PERSON><PERSON><PERSON><PERSON> toutes les évaluations avec tri par date et calcul de la note globale
    $stmt = $db->query("
        SELECT
            id,
            prestataire,
            service,
            qualite,
            delai,
            communication,
            commentaires,
            created_at,
            ROUND((qualite + delai + communication) / 3, 2) as note_globale
        FROM evaluations
        ORDER BY created_at DESC
    ");
    $evaluations = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Statistiques générales
    $statsStmt = $db->query("
        SELECT
            COUNT(*) as total_evaluations,
            COUNT(DISTINCT prestataire) as total_prestataires,
            COUNT(DISTINCT service) as total_services,
            ROUND(AVG(qualite), 2) as avg_qualite,
            ROUND(AVG(delai), 2) as avg_delai,
            ROUND(AVG(communication), 2) as avg_communication,
            ROUND(AVG((qualite + delai + communication) / 3), 2) as avg_globale
        FROM evaluations
    ");
    $stats = $statsStmt->fetch(PDO::FETCH_ASSOC);

} catch(PDOException $e) {
    die("Erreur de base de données : " . $e->getMessage());
}
?>
<?php include 'top-bar-eval.php'; ?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Historique des Évaluations</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f4f4f4;
            color: #333;
            margin: 0;
            padding: 0;
        }
        .container {
            width: 80%;
            margin: 50px auto;
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            padding: 10px;
            border: 1px solid #ddd;
            text-align: left;
        }
        th {
            background-color: #f57c00;
            color: white;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        tr:hover {
            background-color: #f1f1f1;
        }
        .top-bar {
            background-color: #333;
            color: white;
            padding: 10px;
            text-align: center;
        }
        .top-bar a {
            color: white;
            margin: 0 15px;
            text-decoration: none;
        }
        footer {
            background-color: #333;
            color: white;
            text-align: center;
            padding: 10px;
            position: fixed;
            width: 100%;
            bottom: 0;
        }
        .date-info {
            color: #666;
            font-size: 0.9em;
        }

        /* Styles pour les statistiques */
        .stats-section {
            margin-bottom: 30px;
            padding: 20px;
            background-color: #f9f9f9;
            border-radius: 8px;
        }

        .stats-section h2 {
            color: #f57c00;
            margin-bottom: 20px;
            text-align: center;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }

        .stat-card {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            border-left: 4px solid #f57c00;
        }

        .stat-card h3 {
            font-size: 2em;
            color: #f57c00;
            margin: 0 0 10px 0;
        }

        .stat-card p {
            color: #666;
            margin: 0;
            font-weight: bold;
        }

        /* Styles pour le tableau amélioré */
        .table-section h2 {
            color: #f57c00;
            margin-bottom: 20px;
        }

        .rating-cell {
            text-align: center;
            font-weight: bold;
        }

        .global-rating {
            background-color: #f57c00;
            color: white;
            border-radius: 4px;
        }

        .comments-cell {
            max-width: 200px;
            word-wrap: break-word;
        }

        .no-data {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .btn-primary {
            display: inline-block;
            padding: 10px 20px;
            background-color: #f57c00;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            margin-top: 10px;
        }

        .btn-primary:hover {
            background-color: #e67e22;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Historique des Évaluations</h1>

        <!-- Section des statistiques -->
        <?php if ($stats && $stats['total_evaluations'] > 0): ?>
        <div class="stats-section">
            <h2>Statistiques Générales</h2>
            <div class="stats-grid">
                <div class="stat-card">
                    <h3><?= $stats['total_evaluations'] ?></h3>
                    <p>Évaluations totales</p>
                </div>
                <div class="stat-card">
                    <h3><?= $stats['total_prestataires'] ?></h3>
                    <p>Prestataires évalués</p>
                </div>
                <div class="stat-card">
                    <h3><?= $stats['total_services'] ?></h3>
                    <p>Services différents</p>
                </div>
                <div class="stat-card">
                    <h3><?= $stats['avg_globale'] ?>/5</h3>
                    <p>Note moyenne globale</p>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Tableau des évaluations -->
        <div class="table-section">
            <h2>Détail des Évaluations</h2>
            <?php if (count($evaluations) > 0): ?>
            <table>
                <thead>
                    <tr>
                        <th>Date</th>
                        <th>Prestataire</th>
                        <th>Service</th>
                        <th>Qualité</th>
                        <th>Délai</th>
                        <th>Communication</th>
                        <th>Note Globale</th>
                        <th>Commentaires</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($evaluations as $evaluation): ?>
                        <tr>
                            <td class="date-info"><?= date('d/m/Y H:i', strtotime($evaluation['created_at'])) ?></td>
                            <td><?= htmlspecialchars($evaluation['prestataire']) ?></td>
                            <td><?= htmlspecialchars($evaluation['service']) ?></td>
                            <td class="rating-cell"><?= htmlspecialchars($evaluation['qualite']) ?>/5</td>
                            <td class="rating-cell"><?= htmlspecialchars($evaluation['delai']) ?>/5</td>
                            <td class="rating-cell"><?= htmlspecialchars($evaluation['communication']) ?>/5</td>
                            <td class="rating-cell global-rating"><?= htmlspecialchars($evaluation['note_globale']) ?>/5</td>
                            <td class="comments-cell"><?= htmlspecialchars($evaluation['commentaires']) ?></td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
            <?php else: ?>
            <div class="no-data">
                <p>Aucune évaluation trouvée.</p>
                <a href="evaluation.php" class="btn-primary">Créer une première évaluation</a>
            </div>
            <?php endif; ?>
        </div>
    </div>

    <footer>
        <p>&copy; 2025 Schluter Systems. Tous droits réservés.</p>
        <button onclick="changeLanguage('fr')">Français</button>
        <button onclick="changeLanguage('en')">English</button>
        <button onclick="changeLanguage('de')">Deutsch</button>
        <button id="backToTop" style="display:none;" onclick="scrollToTop()">Retour en haut</button>
    </footer>
    <script>
        window.addEventListener('scroll', () => {
            const backToTop = document.getElementById('backToTop');
            backToTop.style.display = window.scrollY > 300 ? 'block' : 'none';
        });

        function scrollToTop() {
            window.scrollTo({ top: 0, behavior: 'smooth' });
        }
    </script>
</body>
</html>
