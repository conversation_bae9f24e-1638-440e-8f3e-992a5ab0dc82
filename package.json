{"name": "schluter-systems-portal", "version": "2.0.0", "description": "Portail intranet modernisé de Schluter Systems", "main": "index.js", "scripts": {"dev": "npm run watch", "build": "npm run build-css && npm run build-js", "build-css": "tailwindcss -i ./css/schluter-unified.css -o ./dist/css/schluter.min.css --minify", "build-js": "terser js/schluter-common.js -o dist/js/schluter.min.js --compress --mangle", "watch": "npm run watch-css & npm run watch-js", "watch-css": "tailwindcss -i ./css/schluter-unified.css -o ./dist/css/schluter.css --watch", "watch-js": "nodemon --watch js/ --ext js --exec \"npm run build-js\"", "optimize": "npm run build && npm run optimize-images", "optimize-images": "imagemin img/**/*.{jpg,png,svg} --out-dir=dist/img", "lint": "eslint js/**/*.js", "format": "prettier --write js/**/*.js css/**/*.css", "serve": "php -S localhost:8000", "test": "echo \"No tests specified\" && exit 0"}, "keywords": ["intranet", "portal", "schluter", "php", "tailwindcss", "modern-ui"], "author": "Schluter Systems", "license": "MIT", "devDependencies": {"@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "autoprefixer": "^10.4.16", "eslint": "^8.55.0", "imagemin": "^8.0.1", "imagemin-mozjpeg": "^10.0.0", "imagemin-pngquant": "^9.0.2", "imagemin-svgo": "^10.0.1", "nodemon": "^3.0.2", "postcss": "^8.4.32", "prettier": "^3.1.1", "tailwindcss": "^3.3.6", "terser": "^5.26.0"}, "dependencies": {"chart.js": "^4.4.0", "gsap": "^3.12.2"}, "browserslist": ["> 1%", "last 2 versions", "not dead"], "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}