<?php
/**
 * Script de vérification de l'état de la base de données
 */

require_once 'config/database.php';

try {
    echo "<h2>Vérification de la base de données Schluter Systems</h2>";
    
    $db = getDB();
    $connection = $db->getConnection();
    
    echo "✅ Connexion à la base de données réussie<br><br>";
    
    // Lister toutes les tables
    $tables = $connection->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
    echo "<h3>Tables existantes:</h3>";
    foreach ($tables as $table) {
        echo "- $table<br>";
    }
    
    echo "<br>";
    
    // Vérifier la table services spécifiquement
    if (in_array('services', $tables)) {
        echo "<h3>Contenu de la table 'services':</h3>";
        $services = $db->fetchAll("SELECT * FROM services");
        if (empty($services)) {
            echo "⚠️ La table services est vide<br>";
        } else {
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
            echo "<tr><th>ID</th><th>Title</th><th>Description</th><th>Icon</th></tr>";
            foreach ($services as $service) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($service['id']) . "</td>";
                echo "<td>" . htmlspecialchars($service['title']) . "</td>";
                echo "<td>" . htmlspecialchars($service['description']) . "</td>";
                echo "<td>" . htmlspecialchars($service['icon']) . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    } else {
        echo "❌ La table 'services' n'existe pas<br>";
    }
    
    echo "<br><a href='index.php'>Retourner à l'accueil</a>";
    
} catch (Exception $e) {
    echo "❌ Erreur: " . $e->getMessage() . "<br>";
    echo "Trace: " . $e->getTraceAsString();
}
?>
