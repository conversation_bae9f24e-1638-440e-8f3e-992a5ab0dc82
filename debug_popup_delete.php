<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Suppression Popup</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            background-color: #f4f4f4;
        }
        .test-form {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .log {
            background-color: #333;
            color: #00ff00;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        button {
            background-color: #f57c00;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        select, input {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>Debug - Suppression de Prestataire</h1>
    
    <div class="test-form">
        <h2>Test de Suppression</h2>
        <form id="testDeleteForm">
            <input type="hidden" name="action" value="removeProvider">
            <label>Prestataire à supprimer :</label>
            <select name="removeItem" id="providerSelect" required>
                <option value="">Chargement...</option>
            </select>
            <button type="submit">Supprimer (avec debug)</button>
        </form>
        
        <button onclick="loadProviders()">Recharger la liste des prestataires</button>
        <button onclick="clearLog()">Effacer le log</button>
    </div>

    <div class="test-form">
        <h2>Log de Debug</h2>
        <div id="debugLog" class="log">Initialisation...\n</div>
    </div>

    <script>
        function log(message) {
            const logDiv = document.getElementById('debugLog');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.textContent += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            document.getElementById('debugLog').textContent = 'Log effacé...\n';
        }

        function loadProviders() {
            log('Chargement des prestataires...');
            
            fetch('evaluation.php?action=getProviders')
                .then(response => {
                    log(`Réponse reçue - Status: ${response.status}`);
                    log(`Content-Type: ${response.headers.get('content-type')}`);
                    return response.json();
                })
                .then(data => {
                    log(`Données reçues: ${JSON.stringify(data)}`);
                    
                    const select = document.getElementById('providerSelect');
                    select.innerHTML = '<option value="">Sélectionnez un prestataire</option>';
                    
                    if (data.providers && data.providers.length > 0) {
                        data.providers.forEach(provider => {
                            const option = new Option(provider, provider);
                            select.add(option);
                        });
                        log(`${data.providers.length} prestataires chargés`);
                    } else {
                        log('Aucun prestataire trouvé');
                    }
                })
                .catch(error => {
                    log(`ERREUR lors du chargement: ${error.message}`);
                    console.error('Error:', error);
                });
        }

        document.getElementById('testDeleteForm').addEventListener('submit', function(event) {
            event.preventDefault();
            
            const formData = new FormData(this);
            const providerToDelete = formData.get('removeItem');
            
            log(`Début de suppression du prestataire: "${providerToDelete}"`);
            
            if (!providerToDelete) {
                log('ERREUR: Aucun prestataire sélectionné');
                alert('Veuillez sélectionner un prestataire');
                return;
            }

            log('Envoi de la requête de suppression...');
            log(`Données envoyées: action=removeProvider, removeItem=${providerToDelete}`);

            fetch('evaluation.php', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                log(`Réponse reçue - Status: ${response.status}`);
                log(`Status Text: ${response.statusText}`);
                log(`Content-Type: ${response.headers.get('content-type')}`);
                
                // Lire la réponse comme texte d'abord pour voir ce qu'on reçoit
                return response.text();
            })
            .then(text => {
                log(`Réponse brute: ${text}`);
                
                try {
                    const data = JSON.parse(text);
                    log(`Réponse JSON parsée: ${JSON.stringify(data)}`);
                    
                    if (data.status === 'success') {
                        log('✅ Suppression réussie!');
                        alert(data.message || 'Prestataire supprimé avec succès!');
                        loadProviders(); // Recharger la liste
                    } else {
                        log(`❌ Erreur: ${data.message}`);
                        alert('Erreur: ' + data.message);
                    }
                } catch (e) {
                    log(`❌ ERREUR: La réponse n'est pas du JSON valide`);
                    log(`Erreur de parsing: ${e.message}`);
                    alert('Erreur: La réponse du serveur n\'est pas au format JSON');
                }
            })
            .catch(error => {
                log(`❌ ERREUR de communication: ${error.message}`);
                console.error('Error:', error);
                alert('Erreur de communication avec le serveur: ' + error.message);
            });
        });

        // Charger les prestataires au démarrage
        document.addEventListener('DOMContentLoaded', function() {
            log('Page chargée, initialisation...');
            loadProviders();
        });
    </script>
</body>
</html>
