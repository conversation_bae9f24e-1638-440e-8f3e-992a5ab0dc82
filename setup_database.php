<?php
/**
 * Script de configuration complète de la base de données Schluter Systems
 */

// Configuration de la base de données
$configs = [
    'development' => [
        'host' => 'localhost',
        'dbname' => 'schluter_db_dev',
        'username' => 'root',
        'password' => ''
    ],
    'production' => [
        'host' => 'localhost',
        'dbname' => 'schluter_db',
        'username' => 'root',
        'password' => ''
    ]
];

// Détecter l'environnement
$env = 'development'; // Par défaut
if (isset($_GET['env']) && in_array($_GET['env'], ['development', 'production'])) {
    $env = $_GET['env'];
}

$config = $configs[$env];

try {
    echo "<h2>Configuration de la base de données Schluter Systems</h2>";
    echo "<p>Environnement: <strong>$env</strong></p>";
    
    // Connexion à MySQL
    $pdo = new PDO("mysql:host={$config['host']}", $config['username'], $config['password']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Créer la base de données
    $pdo->exec("CREATE DATABASE IF NOT EXISTS `{$config['dbname']}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "✅ Base de données '{$config['dbname']}' créée<br>";
    
    // Sélectionner la base de données
    $pdo->exec("USE `{$config['dbname']}`");
    
    // Créer toutes les tables nécessaires
    $tables = [
        'users' => "CREATE TABLE IF NOT EXISTS users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50) NOT NULL UNIQUE,
            password VARCHAR(255) NOT NULL,
            email VARCHAR(100),
            role VARCHAR(20) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )",
        
        'services' => "CREATE TABLE IF NOT EXISTS services (
            id INT AUTO_INCREMENT PRIMARY KEY,
            title VARCHAR(100) NOT NULL,
            description TEXT,
            icon VARCHAR(50),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )",
        
        'evaluations' => "CREATE TABLE IF NOT EXISTS evaluations (
            id INT AUTO_INCREMENT PRIMARY KEY,
            prestataire VARCHAR(100) NOT NULL,
            service VARCHAR(100) NOT NULL,
            qualite INT NOT NULL,
            delai INT NOT NULL,
            communication INT NOT NULL,
            commentaires TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )",
        
        'inventaire' => "CREATE TABLE IF NOT EXISTS inventaire (
            id INT AUTO_INCREMENT PRIMARY KEY,
            nom_article VARCHAR(100) NOT NULL,
            type VARCHAR(50),
            quantite INT NOT NULL,
            description TEXT,
            etat VARCHAR(50),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )",
        
        'tickets' => "CREATE TABLE IF NOT EXISTS tickets (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT,
            title VARCHAR(200) NOT NULL,
            description TEXT,
            priority ENUM('low', 'medium', 'high'),
            status ENUM('open', 'in_progress', 'closed') DEFAULT 'open',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )"
    ];
    
    foreach ($tables as $tableName => $sql) {
        $pdo->exec($sql);
        echo "✅ Table '$tableName' créée<br>";
    }
    
    // Insérer des données par défaut
    $pdo->exec("INSERT IGNORE INTO services (title, description, icon) VALUES
        ('Informatique', 'Gestion des ressources informatiques, inventaire et support technique', 'fas fa-laptop'),
        ('RSE', 'Responsabilité Sociétale des Entreprises et évaluation des prestataires', 'fas fa-leaf'),
        ('ADV', 'Service Après-Vente et support client', 'fas fa-tools'),
        ('Assistance', 'Support technique et demandes d\'aide', 'fas fa-life-ring'),
        ('Tableau de Bord', 'Statistiques et indicateurs de performance', 'fas fa-tachometer-alt'),
        ('Événements', 'Calendrier et gestion des événements', 'fas fa-calendar-alt')
    ");
    echo "✅ Services par défaut ajoutés<br>";
    
    // Créer un utilisateur admin par défaut
    $adminPassword = password_hash('admin123', PASSWORD_DEFAULT);
    $pdo->exec("INSERT IGNORE INTO users (username, password, email, role) VALUES
        ('admin', '$adminPassword', '<EMAIL>', 'admin')
    ");
    echo "✅ Utilisateur admin créé (login: admin, password: admin123)<br>";
    
    // Vérifications finales
    $serviceCount = $pdo->query("SELECT COUNT(*) FROM services")->fetchColumn();
    $userCount = $pdo->query("SELECT COUNT(*) FROM users")->fetchColumn();
    
    echo "<br><h3>Résumé:</h3>";
    echo "- Services: $serviceCount<br>";
    echo "- Utilisateurs: $userCount<br>";
    
    echo "<br><strong>✅ Configuration terminée avec succès!</strong><br>";
    echo "<a href='test_services.php'>Tester les services</a> | ";
    echo "<a href='index.php'>Accéder à l'application</a>";
    
} catch (PDOException $e) {
    echo "❌ Erreur: " . $e->getMessage();
}
?>
