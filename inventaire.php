<?php include 'top-bar-informatique.php';

function getDbConnection() {
    try {
        $conn = new PDO("mysql:host=localhost;dbname=schluter_db", "root", "");
        $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        return $conn;
    } catch(PDOException $e) {
        die("Erreur de connexion : " . $e->getMessage());
    }
}

function loadInventaire() {
    $conn = getDbConnection();
    $stmt = $conn->query("SELECT * FROM inventaire ORDER BY created_at DESC");
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

function addInventaire($nom_article, $type, $quantite, $description, $etat) {
    $conn = getDbConnection();
    $sql = "INSERT INTO inventaire (nom_article, type, quantite, description, etat) VALUES (?, ?, ?, ?, ?)";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$nom_article, $type, $quantite, $description, $etat]);
}

function deleteInventaire($id) {
    $conn = getDbConnection();
    $sql = "DELETE FROM inventaire WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$id]);
}

function updateQuantite($id, $quantite) {
    $conn = getDbConnection();
    $sql = "UPDATE inventaire SET quantite = ? WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$quantite, $id]);
}

$inventaire = loadInventaire();

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    if (isset($_POST['ajouter'])) {
        addInventaire($_POST['nom_article'], $_POST['type'], $_POST['quantite'], $_POST['description'], $_POST['etat']);
    } elseif (isset($_POST['supprimer'])) {
        deleteInventaire($_POST['id_supprimer']);
    } elseif (isset($_POST['modifier_quantite'])) {
        updateQuantite($_POST['id_modifier'], $_POST['quantite_modifier']);
    }
    $inventaire = loadInventaire(); // Reload updated data
}
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Gestion de l'inventaire du matériel informatique - Schluter Systems">
    <title>Inventaire du Service Informatique - Schluter Systems</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        schluter: {
                            orange: '#f57c00',
                            'orange-dark': '#e65100',
                            blue: '#005ea2',
                            'blue-dark': '#004c87',
                            gray: '#333333',
                            'gray-light': '#f4f4f4'
                        }
                    }
                }
            }
        }
    </script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-gray-100">
    <!-- Barre de navigation adaptée pour Tailwind -->
    <header class="fixed top-0 left-0 w-full bg-schluter-gray shadow-lg z-50">
        <div class="flex items-center justify-between px-6 py-4">
            <a href="IT.php" class="flex items-center gap-3 text-white text-xl font-bold hover:text-schluter-orange transition-colors duration-200">
                <img src="img/logo_rgb.svg" alt="Schluter Systems Logo" class="h-10">
                Informatique
            </a>
            <nav class="flex gap-6">
                <a href="accuse_reception.php" class="text-white hover:bg-schluter-orange px-4 py-2 rounded-lg transition-all duration-200 <?= basename($_SERVER['PHP_SELF']) === 'accuse_reception.php' ? 'bg-schluter-orange' : '' ?>">
                    Accusés de Réception
                </a>
                <a href="End_Of_Life.php" class="text-white hover:bg-schluter-orange px-4 py-2 rounded-lg transition-all duration-200 <?= basename($_SERVER['PHP_SELF']) === 'End_Of_Life.php' ? 'bg-schluter-orange' : '' ?>">
                    Changements de Poste
                </a>
                <a href="inventaire.php" class="text-white hover:bg-schluter-orange px-4 py-2 rounded-lg transition-all duration-200 <?= basename($_SERVER['PHP_SELF']) === 'inventaire.php' ? 'bg-schluter-orange' : '' ?>">
                    Inventaire
                </a>
            </nav>
        </div>
    </header>

    <main class="container mx-auto px-4 py-8 mt-20">
        <!-- En-tête avec gradient et statistiques -->
        <div class="bg-gradient-to-r from-schluter-blue to-schluter-orange rounded-lg shadow-lg p-8 mb-8 text-white">
            <div class="flex justify-between items-center mb-6">
                <h1 class="text-4xl font-bold">Inventaire du Service Informatique</h1>
                <a href="historique_inventaire.php" class="bg-white/20 hover:bg-white/30 backdrop-blur-sm text-white px-6 py-3 rounded-lg transition-all duration-300 flex items-center gap-2">
                    <i class="fas fa-history"></i>
                    Voir l'Historique
                </a>
            </div>

            <!-- Statistiques rapides -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="bg-white/20 rounded-lg p-6 backdrop-blur-sm">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-lg opacity-90">Total Articles</p>
                            <h3 class="text-3xl font-bold"><?= count($inventaire) ?></h3>
                        </div>
                        <i class="fas fa-box text-4xl opacity-75"></i>
                    </div>
                    <p class="mt-2 text-sm opacity-75">Articles en inventaire</p>
                </div>

                <div class="bg-white/20 rounded-lg p-6 backdrop-blur-sm">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-lg opacity-90">Quantité Totale</p>
                            <h3 class="text-3xl font-bold"><?= array_sum(array_column($inventaire, 'quantite')) ?></h3>
                        </div>
                        <i class="fas fa-cubes text-4xl opacity-75"></i>
                    </div>
                    <p class="mt-2 text-sm opacity-75">Unités disponibles</p>
                </div>

                <div class="bg-white/20 rounded-lg p-6 backdrop-blur-sm">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-lg opacity-90">Types Différents</p>
                            <h3 class="text-3xl font-bold"><?= count(array_unique(array_column($inventaire, 'type'))) ?></h3>
                        </div>
                        <i class="fas fa-tags text-4xl opacity-75"></i>
                    </div>
                    <p class="mt-2 text-sm opacity-75">Catégories d'équipements</p>
                </div>
            </div>
        </div>

        <!-- Section des formulaires -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
            <!-- Formulaire d'ajout -->
            <div class="bg-white rounded-lg shadow-lg p-6 hover:shadow-xl transition-shadow duration-300 form-card">
                <div class="flex items-center mb-6">
                    <div class="bg-schluter-orange text-white p-3 rounded-lg mr-4">
                        <i class="fas fa-plus text-xl"></i>
                    </div>
                    <h2 class="text-xl font-semibold text-schluter-blue">Ajouter un Matériel</h2>
                </div>
                <form method="POST" class="space-y-4">
                    <div>
                        <label for="nom_article" class="block text-sm font-medium text-gray-700 mb-2">Nom de l'article</label>
                        <input type="text" id="nom_article" name="nom_article" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-schluter-orange focus:border-transparent transition-all duration-200">
                    </div>

                    <div>
                        <label for="type" class="block text-sm font-medium text-gray-700 mb-2">Type</label>
                        <input type="text" id="type" name="type" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-schluter-orange focus:border-transparent transition-all duration-200">
                    </div>

                    <div>
                        <label for="quantite" class="block text-sm font-medium text-gray-700 mb-2">Quantité</label>
                        <input type="number" id="quantite" name="quantite" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-schluter-orange focus:border-transparent transition-all duration-200">
                    </div>

                    <div>
                        <label for="description" class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                        <input type="text" id="description" name="description" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-schluter-orange focus:border-transparent transition-all duration-200">
                    </div>

                    <div>
                        <label for="etat" class="block text-sm font-medium text-gray-700 mb-2">État</label>
                        <input type="text" id="etat" name="etat" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-schluter-orange focus:border-transparent transition-all duration-200">
                    </div>

                    <button type="submit" name="ajouter"
                            class="w-full bg-schluter-orange hover:bg-schluter-orange-dark text-white font-semibold py-3 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center gap-2">
                        <i class="fas fa-plus"></i>
                        Ajouter
                    </button>
                </form>
            </div>

            <!-- Formulaire de suppression -->
            <div class="bg-white rounded-lg shadow-lg p-6 hover:shadow-xl transition-shadow duration-300 form-card">
                <div class="flex items-center mb-6">
                    <div class="bg-red-500 text-white p-3 rounded-lg mr-4">
                        <i class="fas fa-trash text-xl"></i>
                    </div>
                    <h2 class="text-xl font-semibold text-schluter-blue">Supprimer un Matériel</h2>
                </div>
                <form method="POST" class="space-y-4">
                    <div>
                        <label for="id_supprimer" class="block text-sm font-medium text-gray-700 mb-2">ID de l'article à supprimer</label>
                        <input type="number" id="id_supprimer" name="id_supprimer" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent transition-all duration-200">
                    </div>

                    <button type="submit" name="supprimer"
                            class="w-full bg-red-500 hover:bg-red-600 text-white font-semibold py-3 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center gap-2"
                            onclick="return confirm('Êtes-vous sûr de vouloir supprimer cet article ?')">
                        <i class="fas fa-trash"></i>
                        Supprimer
                    </button>
                </form>
            </div>

            <!-- Formulaire de modification -->
            <div class="bg-white rounded-lg shadow-lg p-6 hover:shadow-xl transition-shadow duration-300 form-card">
                <div class="flex items-center mb-6">
                    <div class="bg-schluter-blue text-white p-3 rounded-lg mr-4">
                        <i class="fas fa-edit text-xl"></i>
                    </div>
                    <h2 class="text-xl font-semibold text-schluter-blue">Modifier la Quantité</h2>
                </div>
                <form method="POST" class="space-y-4">
                    <div>
                        <label for="id_modifier" class="block text-sm font-medium text-gray-700 mb-2">ID de l'article</label>
                        <input type="number" id="id_modifier" name="id_modifier" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-schluter-blue focus:border-transparent transition-all duration-200">
                    </div>

                    <div>
                        <label for="quantite_modifier" class="block text-sm font-medium text-gray-700 mb-2">Nouvelle quantité</label>
                        <input type="number" id="quantite_modifier" name="quantite_modifier" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-schluter-blue focus:border-transparent transition-all duration-200">
                    </div>

                    <button type="submit" name="modifier_quantite"
                            class="w-full bg-schluter-blue hover:bg-schluter-blue-dark text-white font-semibold py-3 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center gap-2">
                        <i class="fas fa-edit"></i>
                        Modifier Quantité
                    </button>
                </form>
            </div>
        </div>

        <!-- Section du tableau d'inventaire -->
        <div class="bg-white rounded-lg shadow-lg overflow-hidden">
            <div class="bg-gradient-to-r from-schluter-blue to-schluter-orange p-6">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <i class="fas fa-list text-white text-2xl mr-3"></i>
                        <h2 class="text-2xl font-bold text-white">Liste de l'Inventaire</h2>
                    </div>
                    <div class="text-white text-sm opacity-90">
                        <?= count($inventaire) ?> article(s) au total
                    </div>
                </div>
            </div>

            <div class="overflow-x-auto">
                <?php if (empty($inventaire)): ?>
                    <div class="p-8 text-center">
                        <i class="fas fa-box-open text-gray-400 text-6xl mb-4"></i>
                        <h3 class="text-xl font-semibold text-gray-600 mb-2">Aucun article en inventaire</h3>
                        <p class="text-gray-500">Commencez par ajouter votre premier matériel informatique.</p>
                    </div>
                <?php else: ?>
                    <table class="w-full">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    <i class="fas fa-hashtag mr-2"></i>ID
                                </th>
                                <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    <i class="fas fa-tag mr-2"></i>Nom de l'article
                                </th>
                                <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    <i class="fas fa-folder mr-2"></i>Type
                                </th>
                                <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    <i class="fas fa-cubes mr-2"></i>Quantité
                                </th>
                                <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    <i class="fas fa-info-circle mr-2"></i>Description
                                </th>
                                <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    <i class="fas fa-check-circle mr-2"></i>État
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <?php foreach ($inventaire as $index => $row): ?>
                                <tr class="hover:bg-gray-50 transition-colors duration-200 inventory-row">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                        <span class="bg-schluter-blue text-white px-2 py-1 rounded-full text-xs">
                                            <?= htmlspecialchars($row['id']) ?>
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-semibold">
                                        <?= htmlspecialchars($row['nom_article']) ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                                        <span class="bg-gray-100 text-gray-800 px-2 py-1 rounded-lg text-xs">
                                            <?= htmlspecialchars($row['type']) ?>
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <div class="flex items-center">
                                            <span class="bg-schluter-orange text-white px-3 py-1 rounded-full text-sm font-semibold">
                                                <?= htmlspecialchars($row['quantite']) ?>
                                            </span>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 text-sm text-gray-600 max-w-xs truncate">
                                        <?= htmlspecialchars($row['description']) ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm">
                                        <?php
                                        $etat = strtolower(htmlspecialchars($row['etat']));
                                        $badgeClass = 'bg-gray-100 text-gray-800';
                                        $icon = 'fas fa-question-circle';

                                        if (strpos($etat, 'bon') !== false || strpos($etat, 'neuf') !== false) {
                                            $badgeClass = 'bg-green-100 text-green-800';
                                            $icon = 'fas fa-check-circle';
                                        } elseif (strpos($etat, 'moyen') !== false || strpos($etat, 'correct') !== false) {
                                            $badgeClass = 'bg-yellow-100 text-yellow-800';
                                            $icon = 'fas fa-exclamation-circle';
                                        } elseif (strpos($etat, 'mauvais') !== false || strpos($etat, 'défaillant') !== false) {
                                            $badgeClass = 'bg-red-100 text-red-800';
                                            $icon = 'fas fa-times-circle';
                                        }
                                        ?>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?= $badgeClass ?>">
                                            <i class="<?= $icon ?> mr-1"></i>
                                            <?= htmlspecialchars($row['etat']) ?>
                                        </span>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                <?php endif; ?>
            </div>
        </div>
    </main>

    <!-- Scripts d'animation -->
    <script>
        // Animation des cartes au chargement
        document.addEventListener('DOMContentLoaded', () => {
            // Animation des cartes de formulaire
            gsap.from('.form-card', {
                duration: 0.6,
                y: 50,
                opacity: 0,
                stagger: 0.2,
                ease: 'power2.out'
            });

            // Animation des lignes du tableau
            gsap.from('.inventory-row', {
                duration: 0.4,
                x: -20,
                opacity: 0,
                stagger: 0.05,
                ease: 'power2.out',
                delay: 0.8
            });

            // Animation de l'en-tête
            gsap.from('.bg-gradient-to-r', {
                duration: 0.8,
                scale: 0.95,
                opacity: 0,
                ease: 'power2.out'
            });
        });

        // Confirmation de suppression améliorée
        function confirmDelete(id, nom) {
            return confirm(`Êtes-vous sûr de vouloir supprimer l'article "${nom}" (ID: ${id}) ?\n\nCette action est irréversible.`);
        }

        // Auto-focus sur les champs de formulaire
        document.querySelectorAll('input[type="text"], input[type="number"]').forEach(input => {
            input.addEventListener('focus', function() {
                this.style.transform = 'scale(1.02)';
            });

            input.addEventListener('blur', function() {
                this.style.transform = 'scale(1)';
            });
        });
    </script>

    <?php include 'bottom_bar.php'; ?>
</body>
</html>
