<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once('lib/FPDF/fpdf.php');

// Fonction de connexion à la base de données
function getDbConnection() {
    try {
        $conn = new PDO("mysql:host=localhost;dbname=schluter_db", "root", "");
        $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        return $conn;
    } catch(PDOException $e) {
        die("Erreur de connexion : " . $e->getMessage());
    }
}

function addAccuseReception($collaborateur, $responsable, $date, $remis, $recu) {
    $conn = getDbConnection();
    $sql = "INSERT INTO accuses_reception (collaborateur, responsable, date_creation, materiel_remis, materiel_recu)
            VALUES (:collaborateur, :responsable, :date, :remis, :recu)";

    $stmt = $conn->prepare($sql);
    $stmt->execute([
        ':collaborateur' => $collaborateur,
        ':responsable' => $responsable,
        ':date' => $date,
        ':remis' => $remis,
        ':recu' => $recu
    ]);
}

function deleteAccuseReception($deleteId) {
    $conn = getDbConnection();
    $sql = "DELETE FROM accuses_reception WHERE id = :id";

    $stmt = $conn->prepare($sql);
    $stmt->execute([':id' => $deleteId]);

    header("Location: http://localhost/sauvé/accuse_reception.php");
    exit();
}

// Fonction pour ajouter une image en haut à droite
function addImageToPDF($pdf, $imagePath) {
    if (file_exists($imagePath)) {
        $pdf->Image($imagePath, 160, 10, 30); // Ajustez les coordonnées et la taille selon vos besoins
    } else {
        echo "L'image n'a pas été trouvée à l'emplacement spécifié.";
    }
}

function generatePDF($data) {
    $pdf = new FPDF();
    $pdf->AddPage();
    $pdf->SetMargins(20, 20, 20);

    // Ajouter le logo de l'entreprise en haut à droite
    addImageToPDF($pdf, 'C:/laragon/www/projet/img/schluter-systems-logo-png-transparent.png');

    // Couleurs de l'entreprise
    $pdf->SetTextColor(0, 70, 150); // Exemple de couleur, à ajuster selon les couleurs de Schluter Systems

    // Titre principal en haut à gauche
    $pdf->SetFont('Arial', 'B', 35);
    $pdf->SetY(10);
    $pdf->Cell(0, 10, 'Accuse de reception', 0, 1, 'L');
    $pdf->Ln(20);

    // En-tête et pied de page
    $pdf->SetAutoPageBreak(true, 30);
    $pdf->AliasNbPages();

    foreach ($data as $ar) {
        generateSinglePDFSection($pdf, $ar);
        if (next($data)) {
            $pdf->AddPage();
            // Ajouter le logo de l'entreprise en haut à droite pour chaque page
            addImageToPDF($pdf, 'C:/laragon/www/projet/img/schluter-systems-logo-png-transparent.png');
        }
    }

    $filename = 'accuses_reception_' . date('Y-m-d_H-i-s') . '.pdf';
    ob_clean();
    $pdf->Output('F', $filename);
    return $filename;
}

function generateSinglePDFSection($pdf, $ar) {
    $pdf->SetFont('Arial', 'I', 15);
    $pdf->SetTextColor(50, 50, 50);
    $pdf->Cell(0, 10, "Je soussigne " . htmlspecialchars($ar['Collaborateur']), 0, 1, 'L');
    $pdf->Cell(0, 10, "Atteste avoir remis le(s) materiel(s) ou le(s) document(s) suivant(s) :", 0, 1, 'L');
    $pdf->Ln(10);
    $pdf->SetFont('Arial', '', 12);
    $pdf->SetTextColor(0, 0, 0);
    $pdf->MultiCell(0, 10, htmlspecialchars($ar['Remis']), 0, 'L');
    $pdf->Ln(20);
    $pdf->SetFont('Arial', 'I', 15);
    $pdf->SetTextColor(50, 50, 50);
    $pdf->Cell(0, 10, "Atteste avoir recu le(s) materiel(s) ou le(s) document(s) suivant(s) :", 0, 1, 'L');
    $pdf->Ln(10);
    $pdf->SetFont('Arial', '', 12);
    $pdf->SetTextColor(0, 0, 0);
    $pdf->MultiCell(0, 10, htmlspecialchars($ar['Recu']), 0, 'L');
    $pdf->Ln(30);
    $pdf->SetFont('Arial', '', 12);
    $pdf->Cell(0, 10, "Du Service Informatique par " . htmlspecialchars($ar['Responsable']), 0, 1, 'L');
    $pdf->Cell(0, 10, "En date du " . date('d/m/Y', strtotime($ar['Date'])), 0, 1, 'L');
    $pdf->Ln(20);
    $pdf->SetFont('Arial', 'B', 15);
    $pdf->Cell(95, 10, "Bon pour accord du responsable", 1, 0, 'C');
    $pdf->Cell(95, 10, "Bon pour accord du collaborateur", 1, 1, 'C');
    $pdf->Ln(20);
    $pdf->SetDrawColor(200, 200, 200);
    $pdf->Cell(0, 0, '', 'T', 1, 'C');
    $pdf->Ln(20);
    $bottom_margin = 30;
    while ($pdf->GetY() < $pdf->GetPageHeight() - $bottom_margin) {
        $pdf->Ln(10);
    }
}

// Fonction pour ajouter un pied de page
function Footer($pdf) {
    $pdf->SetY(-15);
    $pdf->SetFont('Arial', 'I', 8);
    $pdf->SetTextColor(100, 100, 100);
    $pdf->Cell(0, 10, 'Page ' . $pdf->PageNo() . '/{nb}', 0, 0, 'C');
}

function generateSinglePDF($ar) {
    $pdf = new FPDF();
    $pdf->AddPage();
    $pdf->SetMargins(20, 20, 20);

    // Ajouter le logo de l'entreprise en haut à droite
    addImageToPDF($pdf, 'C:/laragon/www/projet/img/schluter-systems-logo-png-transparent.png');

    // Titre principal en haut à gauche
    $pdf->SetFont('Arial', 'B', 35);
    $pdf->SetY(10);
    $pdf->Cell(0, 10, 'Accuse de reception', 0, 1, 'L');
    $pdf->Ln(20);

    generateSinglePDFSection($pdf, $ar);

    $filename = 'accuse_reception_' . $ar['ID'] . '_' . date('Y-m-d_H-i-s') . '.pdf';
    ob_clean();
    $pdf->Output('F', $filename);
    return $filename;
}

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $collaborateur = htmlspecialchars($_POST['Collaborateur']);
    $responsable = htmlspecialchars($_POST['Responsable']);
    $date = htmlspecialchars($_POST['Date']);
    $remis = htmlspecialchars($_POST['Remis']);
    $reçu = htmlspecialchars($_POST['Reçu']);

    addAccuseReception($collaborateur, $responsable, $date, $remis, $reçu);
}

if (isset($_GET['delete_id'])) {
    $deleteId = intval($_GET['delete_id']);
    deleteAccuseReception($deleteId);
}

// Récupération des données depuis la base de données
$conn = getDbConnection();
$sql = "SELECT id as ID, collaborateur as Collaborateur, responsable as Responsable,
        date_creation as Date, materiel_remis as Remis, materiel_recu as Recu
        FROM accuses_reception
        ORDER BY date_creation DESC LIMIT 10";
$stmt = $conn->query($sql);
$arData = $stmt->fetchAll(PDO::FETCH_ASSOC);

if (isset($_GET['download_pdf'])) {
    $pdfFile = generatePDF($arData);

    header("Content-Type: application/pdf");
    header("Content-Disposition: attachment; filename=" . basename($pdfFile));
    readfile($pdfFile);
    exit();
}

if (isset($_GET['download_single_pdf'])) {
    $id = intval($_GET['download_single_pdf']);
    $ar = null;
    foreach ($arData as $item) {
        if ($item['ID'] == $id) {
            $ar = $item;
            break;
        }
    }

    if ($ar) {
        $pdfFile = generateSinglePDF($ar);

        header("Content-Disposition: attachment; filename=" . basename($pdfFile));
        header("Content-Type: application/pdf");
        readfile($pdfFile);
        exit;
    }
}

?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Gestion des accusés de réception - Schluter Systems">
    <title>Accusés de Réception - Schluter Systems</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        schluter: {
                            orange: '#f57c00',
                            'orange-dark': '#e65100',
                            blue: '#005ea2',
                            'blue-dark': '#004c87',
                            gray: '#333333',
                            'gray-light': '#f4f4f4'
                        }
                    }
                }
            }
        }
    </script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-gray-100">
    <!-- Barre de navigation adaptée pour Tailwind -->
    <header class="fixed top-0 left-0 w-full bg-schluter-gray shadow-lg z-50">
        <div class="flex items-center justify-between px-6 py-4">
            <a href="IT.php" class="flex items-center gap-3 text-white text-xl font-bold hover:text-schluter-orange transition-colors duration-200">
                <img src="img/logo_rgb.svg" alt="Schluter Systems Logo" class="h-10">
                Informatique
            </a>
            <nav class="flex gap-6">
                <a href="accuse_reception.php" class="text-white hover:bg-schluter-orange px-4 py-2 rounded-lg transition-all duration-200 <?= basename($_SERVER['PHP_SELF']) === 'accuse_reception.php' ? 'bg-schluter-orange' : '' ?>">
                    Accusés de Réception
                </a>
                <a href="End_Of_Life.php" class="text-white hover:bg-schluter-orange px-4 py-2 rounded-lg transition-all duration-200 <?= basename($_SERVER['PHP_SELF']) === 'End_Of_Life.php' ? 'bg-schluter-orange' : '' ?>">
                    Changements de Poste
                </a>
                <a href="inventaire.php" class="text-white hover:bg-schluter-orange px-4 py-2 rounded-lg transition-all duration-200 <?= basename($_SERVER['PHP_SELF']) === 'inventaire.php' ? 'bg-schluter-orange' : '' ?>">
                    Inventaire
                </a>
            </nav>
        </div>
    </header>

    <main class="container mx-auto px-4 py-8 mt-20">
        <!-- En-tête avec gradient et statistiques -->
        <div class="bg-gradient-to-r from-schluter-blue to-schluter-orange rounded-lg shadow-lg p-8 mb-8 text-white">
            <div class="flex justify-between items-center mb-6">
                <h1 class="text-4xl font-bold">Gestion des Accusés de Réception</h1>
                <div class="flex items-center gap-4">
                    <a href="historique.php" class="bg-white/20 hover:bg-white/30 backdrop-blur-sm text-white px-6 py-3 rounded-lg transition-all duration-300 flex items-center gap-2">
                        <i class="fas fa-history"></i>
                        Voir l'Historique
                    </a>
                    <a href="?download_pdf=1" class="bg-white/20 hover:bg-white/30 backdrop-blur-sm text-white px-6 py-3 rounded-lg transition-all duration-300 flex items-center gap-2">
                        <i class="fas fa-download"></i>
                        Télécharger Tout
                    </a>
                </div>
            </div>

            <!-- Statistiques rapides -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="bg-white/20 rounded-lg p-6 backdrop-blur-sm">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-lg opacity-90">Total Accusés</p>
                            <h3 class="text-3xl font-bold"><?= count($arData) ?></h3>
                        </div>
                        <i class="fas fa-file-signature text-4xl opacity-75"></i>
                    </div>
                    <p class="mt-2 text-sm opacity-75">Accusés de réception</p>
                </div>

                <div class="bg-white/20 rounded-lg p-6 backdrop-blur-sm">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-lg opacity-90">Ce Mois</p>
                            <h3 class="text-3xl font-bold">
                                <?php
                                $thisMonth = array_filter($arData, function($ar) {
                                    return date('Y-m', strtotime($ar['Date'])) === date('Y-m');
                                });
                                echo count($thisMonth);
                                ?>
                            </h3>
                        </div>
                        <i class="fas fa-calendar-month text-4xl opacity-75"></i>
                    </div>
                    <p class="mt-2 text-sm opacity-75">Accusés ce mois-ci</p>
                </div>

                <div class="bg-white/20 rounded-lg p-6 backdrop-blur-sm">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-lg opacity-90">Dernière Activité</p>
                            <h3 class="text-lg font-bold">
                                <?= !empty($arData) ? date('d/m/Y', strtotime($arData[0]['Date'])) : 'Aucune' ?>
                            </h3>
                        </div>
                        <i class="fas fa-clock text-4xl opacity-75"></i>
                    </div>
                    <p class="mt-2 text-sm opacity-75">Dernier accusé créé</p>
                </div>
            </div>
        </div>

        <!-- Section du formulaire -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-8 form-card">
            <div class="flex items-center mb-6">
                <div class="bg-schluter-orange text-white p-3 rounded-lg mr-4">
                    <i class="fas fa-file-signature text-xl"></i>
                </div>
                <h2 class="text-2xl font-semibold text-schluter-blue">Créer un Accusé de Réception</h2>
            </div>

            <form method="POST" action="" class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="Collaborateur" class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-user mr-2 text-schluter-blue"></i>Collaborateur
                    </label>
                    <input type="text" id="Collaborateur" name="Collaborateur" required
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-schluter-orange focus:border-transparent transition-all duration-200"
                           placeholder="Nom du collaborateur">
                </div>

                <div>
                    <label for="Responsable" class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-user-tie mr-2 text-schluter-blue"></i>Responsable
                    </label>
                    <input type="text" id="Responsable" name="Responsable" required
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-schluter-orange focus:border-transparent transition-all duration-200"
                           placeholder="Nom du responsable">
                </div>

                <div>
                    <label for="Date" class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-calendar mr-2 text-schluter-blue"></i>Date
                    </label>
                    <input type="date" id="Date" name="Date" required
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-schluter-orange focus:border-transparent transition-all duration-200">
                </div>

                <div>
                    <!-- Espace pour équilibrer la grille -->
                </div>

                <div class="md:col-span-2">
                    <label for="Remis" class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-hand-holding mr-2 text-schluter-blue"></i>Matériel/Documents Remis
                    </label>
                    <textarea id="Remis" name="Remis" required rows="3"
                              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-schluter-orange focus:border-transparent transition-all duration-200"
                              placeholder="Décrivez le matériel ou les documents remis..."></textarea>
                </div>

                <div class="md:col-span-2">
                    <label for="Reçu" class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-inbox mr-2 text-schluter-blue"></i>Matériel/Documents Reçus
                    </label>
                    <textarea id="Reçu" name="Reçu" required rows="3"
                              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-schluter-orange focus:border-transparent transition-all duration-200"
                              placeholder="Décrivez le matériel ou les documents reçus..."></textarea>
                </div>

                <div class="md:col-span-2">
                    <button type="submit"
                            class="w-full bg-schluter-orange hover:bg-schluter-orange-dark text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-200 flex items-center justify-center gap-2">
                        <i class="fas fa-plus"></i>
                        Créer l'Accusé de Réception
                    </button>
                </div>
            </form>
        </div>

        <!-- Section du tableau des accusés de réception -->
        <div class="bg-white rounded-lg shadow-lg overflow-hidden">
            <div class="bg-gradient-to-r from-schluter-blue to-schluter-orange p-6">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <i class="fas fa-list text-white text-2xl mr-3"></i>
                        <h2 class="text-2xl font-bold text-white">Liste des Accusés de Réception</h2>
                    </div>
                    <div class="text-white text-sm opacity-90">
                        <?= count($arData) ?> accusé(s) au total
                    </div>
                </div>
            </div>

            <div class="overflow-x-auto">
                <?php if (empty($arData)): ?>
                    <div class="p-8 text-center">
                        <i class="fas fa-file-signature text-gray-400 text-6xl mb-4"></i>
                        <h3 class="text-xl font-semibold text-gray-600 mb-2">Aucun accusé de réception</h3>
                        <p class="text-gray-500">Commencez par créer votre premier accusé de réception.</p>
                    </div>
                <?php else: ?>
                    <table class="w-full">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    <i class="fas fa-user mr-2"></i>Collaborateur
                                </th>
                                <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    <i class="fas fa-user-tie mr-2"></i>Responsable
                                </th>
                                <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    <i class="fas fa-calendar mr-2"></i>Date
                                </th>
                                <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    <i class="fas fa-hand-holding mr-2"></i>Remis
                                </th>
                                <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    <i class="fas fa-inbox mr-2"></i>Reçu
                                </th>
                                <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    <i class="fas fa-cog mr-2"></i>Actions
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <?php foreach ($arData as $index => $ar): ?>
                                <tr class="hover:bg-gray-50 transition-colors duration-200 table-row">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                        <div class="flex items-center">
                                            <div class="bg-schluter-blue text-white p-2 rounded-full mr-3">
                                                <i class="fas fa-user text-sm"></i>
                                            </div>
                                            <?= htmlspecialchars($ar['Collaborateur']) ?>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                                        <div class="flex items-center">
                                            <div class="bg-schluter-orange text-white p-2 rounded-full mr-3">
                                                <i class="fas fa-user-tie text-sm"></i>
                                            </div>
                                            <?= htmlspecialchars($ar['Responsable']) ?>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <div class="flex items-center">
                                            <i class="fas fa-calendar text-schluter-blue mr-2"></i>
                                            <?= date('d/m/Y', strtotime($ar['Date'])) ?>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 text-sm text-gray-600 max-w-xs">
                                        <div class="truncate" title="<?= htmlspecialchars($ar['Remis']) ?>">
                                            <span class="bg-green-100 text-green-800 px-2 py-1 rounded-lg text-xs">
                                                <?= strlen($ar['Remis']) > 30 ? substr(htmlspecialchars($ar['Remis']), 0, 30) . '...' : htmlspecialchars($ar['Remis']) ?>
                                            </span>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 text-sm text-gray-600 max-w-xs">
                                        <div class="truncate" title="<?= htmlspecialchars($ar['Recu']) ?>">
                                            <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded-lg text-xs">
                                                <?= strlen($ar['Recu']) > 30 ? substr(htmlspecialchars($ar['Recu']), 0, 30) . '...' : htmlspecialchars($ar['Recu']) ?>
                                            </span>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm">
                                        <div class="flex items-center gap-2">
                                            <a href="?download_single_pdf=<?= htmlspecialchars($ar['ID']) ?>"
                                               class="bg-schluter-blue hover:bg-schluter-blue-dark text-white px-3 py-1 rounded-lg transition-colors duration-200 flex items-center gap-1"
                                               title="Télécharger le PDF">
                                                <i class="fas fa-download text-xs"></i>
                                                PDF
                                            </a>
                                            <a href="?delete_id=<?= htmlspecialchars($ar['ID']) ?>"
                                               class="bg-red-500 hover:bg-red-600 text-white px-3 py-1 rounded-lg transition-colors duration-200 flex items-center gap-1"
                                               onclick="return confirm('Êtes-vous sûr de vouloir supprimer cet accusé de réception ?')"
                                               title="Supprimer">
                                                <i class="fas fa-trash text-xs"></i>
                                                Supprimer
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                <?php endif; ?>
            </div>
        </div>
    </main>

    <!-- Scripts d'animation et fonctionnalités -->
    <script>
        // Animation des éléments au chargement
        document.addEventListener('DOMContentLoaded', () => {
            // Animation du formulaire
            gsap.from('.form-card', {
                duration: 0.6,
                y: 50,
                opacity: 0,
                ease: 'power2.out'
            });

            // Animation des lignes du tableau
            gsap.from('.table-row', {
                duration: 0.4,
                x: -20,
                opacity: 0,
                stagger: 0.05,
                ease: 'power2.out',
                delay: 0.8
            });

            // Animation de l'en-tête
            gsap.from('.bg-gradient-to-r', {
                duration: 0.8,
                scale: 0.95,
                opacity: 0,
                ease: 'power2.out'
            });
        });

        // Effets sur les champs de formulaire
        document.querySelectorAll('input, textarea').forEach(input => {
            input.addEventListener('focus', function() {
                this.style.transform = 'scale(1.02)';
            });

            input.addEventListener('blur', function() {
                this.style.transform = 'scale(1)';
            });
        });

        // Auto-définir la date d'aujourd'hui par défaut
        document.getElementById('Date').valueAsDate = new Date();

        // Fonction pour afficher le contenu complet dans une modal (optionnel)
        function showFullContent(content, title) {
            alert(title + ':\n\n' + content);
        }
    </script>

    <?php include 'bottom_bar.php'; ?>
</body>
</html>